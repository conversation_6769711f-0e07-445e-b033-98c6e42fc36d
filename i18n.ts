// خدمة الترجمة لتطبيق تحليل العملات الرقمية

// قاموس الترجمات
const translations = {
  ar: {
    // العناوين الرئيسية
    appTitle: 'تطبيق تحليل العملات الرقمية',
    settings: 'الإعدادات',
    logout: 'تسجيل الخروج',
    login: 'تسجيل الدخول',
    signup: 'إنشاء حساب',
    
    // المؤشرات الفنية
    technicalIndicators: 'المؤشرات الفنية',
    rsiDivergence: 'تباعد مؤشر القوة النسبية',
    obvDivergence: 'تباعد حجم التداول',
    macdDivergence: 'تباعد مؤشر الماكد',
    movingAverageCrossover: 'تقاطع المتوسطات المتحركة',
    
    // الإطارات الزمنية
    timeframes: 'الأطر الزمنية',
    hours4: '4 ساعات',
    hours3: '3 ساعات',
    hours2: 'ساعتين',
    daily: 'يومي',
    weekly: 'أسبوعي',
    
    // إشارات التداول
    tradingSignals: 'إشارات البيع والشراء',
    buySignal: 'إشارة شراء',
    sellSignal: 'إشارة بيع',
    reason: 'السبب',
    indicator: 'المؤشر',
    price: 'السعر',
    noSignals: 'لا توجد إشارات حالياً',
    
    // الإشعارات
    notifications: 'الإشعارات',
    enableNotifications: 'تفعيل الإشعارات',
    notificationsEnabled: 'الإشعارات مفعلة. ستتلقى تنبيهات عند ظهور إشارات جديدة.',
    notificationsDisabled: 'الإشعارات غير مفعلة. فعّل الإشعارات لتلقي تنبيهات عند ظهور إشارات جديدة.',
    initializingNotifications: 'جاري تهيئة الإشعارات...',
    newBuySignal: 'إشارة شراء جديدة',
    newSellSignal: 'إشارة بيع جديدة',
    
    // إعدادات المستخدم
    userSettings: 'إعدادات المستخدم',
    preferredLanguage: 'اللغة المفضلة',
    preferredTimeframe: 'الإطار الزمني المفضل',
    activeIndicators: 'المؤشرات الفنية النشطة',
    saveSettings: 'حفظ الإعدادات',
    settingsSaved: 'تم حفظ الإعدادات بنجاح',
    
    // المصادقة
    email: 'البريد الإلكتروني',
    password: 'كلمة المرور',
    createAccount: 'ليس لديك حساب؟ إنشاء حساب جديد',
    haveAccount: 'لديك حساب بالفعل؟ تسجيل الدخول',
    
    // رسائل الحالة
    loading: 'جاري تحميل البيانات...',
    error: 'حدث خطأ. يرجى المحاولة مرة أخرى.',
    loadingError: 'فشل تحميل بيانات السوق. يرجى المحاولة مرة أخرى لاحقاً.',
    
    // أخرى
    copyright: '© 2025 تطبيق تحليل العملات الرقمية - جميع الحقوق محفوظة'
  },
  en: {
    // Main Titles
    appTitle: 'Crypto Technical Analysis App',
    settings: 'Settings',
    logout: 'Logout',
    login: 'Login',
    signup: 'Sign Up',
    
    // Technical Indicators
    technicalIndicators: 'Technical Indicators',
    rsiDivergence: 'RSI Divergence',
    obvDivergence: 'OBV Divergence',
    macdDivergence: 'MACD Divergence',
    movingAverageCrossover: 'Moving Average Crossover',
    
    // Timeframes
    timeframes: 'Timeframes',
    hours4: '4 Hours',
    hours3: '3 Hours',
    hours2: '2 Hours',
    daily: 'Daily',
    weekly: 'Weekly',
    
    // Trading Signals
    tradingSignals: 'Trading Signals',
    buySignal: 'Buy Signal',
    sellSignal: 'Sell Signal',
    reason: 'Reason',
    indicator: 'Indicator',
    price: 'Price',
    noSignals: 'No signals available',
    
    // Notifications
    notifications: 'Notifications',
    enableNotifications: 'Enable Notifications',
    notificationsEnabled: 'Notifications are enabled. You will receive alerts for new signals.',
    notificationsDisabled: 'Notifications are disabled. Enable notifications to receive alerts for new signals.',
    initializingNotifications: 'Initializing notifications...',
    newBuySignal: 'New Buy Signal',
    newSellSignal: 'New Sell Signal',
    
    // User Settings
    userSettings: 'User Settings',
    preferredLanguage: 'Preferred Language',
    preferredTimeframe: 'Preferred Timeframe',
    activeIndicators: 'Active Technical Indicators',
    saveSettings: 'Save Settings',
    settingsSaved: 'Settings saved successfully',
    
    // Authentication
    email: 'Email',
    password: 'Password',
    createAccount: 'Don\'t have an account? Create new account',
    haveAccount: 'Already have an account? Login',
    
    // Status Messages
    loading: 'Loading data...',
    error: 'An error occurred. Please try again.',
    loadingError: 'Failed to load market data. Please try again later.',
    
    // Others
    copyright: '© 2025 Crypto Technical Analysis App - All Rights Reserved'
  }
};

/**
 * الحصول على ترجمة نص معين
 * @param key مفتاح النص
 * @param language رمز اللغة ('ar' أو 'en')
 */
export const translate = (key: string, language: string = 'ar'): string => {
  // التحقق من وجود اللغة
  if (!translations[language as keyof typeof translations]) {
    language = 'ar'; // استخدام العربية كلغة افتراضية
  }
  
  // الحصول على الترجمة
  const translation = translations[language as keyof typeof translations][key as keyof typeof translations.ar];
  
  // إرجاع الترجمة أو المفتاح إذا لم تكن الترجمة موجودة
  return translation || key;
};

/**
 * الحصول على قائمة اللغات المدعومة
 */
export const getSupportedLanguages = (): { code: string, name: string }[] => {
  return [
    { code: 'ar', name: 'العربية' },
    { code: 'en', name: 'English' }
  ];
};

/**
 * التحقق من اتجاه اللغة
 * @param language رمز اللغة
 */
export const getLanguageDirection = (language: string): 'rtl' | 'ltr' => {
  return language === 'ar' ? 'rtl' : 'ltr';
};

/**
 * تنسيق رقم حسب اللغة
 * @param number الرقم
 * @param language رمز اللغة
 */
export const formatNumber = (number: number, language: string): string => {
  return new Intl.NumberFormat(language === 'ar' ? 'ar-EG' : 'en-US').format(number);
};

/**
 * تنسيق تاريخ حسب اللغة
 * @param date التاريخ
 * @param language رمز اللغة
 */
export const formatDate = (date: Date | string, language: string): string => {
  if (typeof date === 'string') {
    date = new Date(date);
  }
  
  return new Intl.DateTimeFormat(
    language === 'ar' ? 'ar-EG' : 'en-US',
    { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' }
  ).format(date);
};
