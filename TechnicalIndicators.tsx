import React from 'react';

interface TechnicalIndicatorsProps {
  indicators: {
    rsiDivergence: boolean;
    obvDivergence: boolean;
    macdDivergence: boolean;
    movingAverageCrossover: boolean;
  };
  onToggleIndicator: (indicator: string) => void;
}

const TechnicalIndicators: React.FC<TechnicalIndicatorsProps> = ({ indicators, onToggleIndicator }) => {
  return (
    <div className="technical-indicators card mb-4">
      <div className="card-header">
        المؤشرات الفنية / Technical Indicators
      </div>
      <div className="card-body">
        <div className="form-check form-switch mb-2">
          <input
            className="form-check-input"
            type="checkbox"
            id="rsiDivergence"
            checked={indicators.rsiDivergence}
            onChange={() => onToggleIndicator('rsiDivergence')}
          />
          <label className="form-check-label" htmlFor="rsiDivergence">
            RSI Divergence
          </label>
        </div>
        
        <div className="form-check form-switch mb-2">
          <input
            className="form-check-input"
            type="checkbox"
            id="obvDivergence"
            checked={indicators.obvDivergence}
            onChange={() => onToggleIndicator('obvDivergence')}
          />
          <label className="form-check-label" htmlFor="obvDivergence">
            OBV Divergence
          </label>
        </div>
        
        <div className="form-check form-switch mb-2">
          <input
            className="form-check-input"
            type="checkbox"
            id="macdDivergence"
            checked={indicators.macdDivergence}
            onChange={() => onToggleIndicator('macdDivergence')}
          />
          <label className="form-check-label" htmlFor="macdDivergence">
            MACD Divergence
          </label>
        </div>
        
        <div className="form-check form-switch">
          <input
            className="form-check-input"
            type="checkbox"
            id="movingAverageCrossover"
            checked={indicators.movingAverageCrossover}
            onChange={() => onToggleIndicator('movingAverageCrossover')}
          />
          <label className="form-check-label" htmlFor="movingAverageCrossover">
            Moving Average Crossover
          </label>
        </div>
      </div>
    </div>
  );
};

export default TechnicalIndicators;
