import React, { useState } from 'react';
import { initializeApp } from 'firebase/app';
import { 
  getAuth, 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User
} from 'firebase/auth';
import { 
  getFirestore, 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc 
} from 'firebase/firestore';

// تكوين Firebase
const firebaseConfig = {
  apiKey: "YOUR_API_KEY",
  authDomain: "YOUR_AUTH_DOMAIN",
  projectId: "YOUR_PROJECT_ID",
  storageBucket: "YOUR_STORAGE_BUCKET",
  messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
  appId: "YOUR_APP_ID"
};

// تهيئة Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// واجهة إعدادات المستخدم
export interface UserSettings {
  preferredLanguage: string;
  preferredTimeframe: string;
  activeIndicators: {
    rsiDivergence: boolean;
    obvDivergence: boolean;
    macdDivergence: boolean;
    movingAverageCrossover: boolean;
  };
  favoriteCoins: string[];
  notificationsEnabled: boolean;
}

// إعدادات المستخدم الافتراضية
export const defaultUserSettings: UserSettings = {
  preferredLanguage: 'ar',
  preferredTimeframe: '4h',
  activeIndicators: {
    rsiDivergence: true,
    obvDivergence: true,
    macdDivergence: true,
    movingAverageCrossover: true,
  },
  favoriteCoins: ['bitcoin', 'ethereum'],
  notificationsEnabled: true
};

// واجهة سياق المصادقة
interface AuthContextType {
  currentUser: User | null;
  userSettings: UserSettings;
  loading: boolean;
  error: string | null;
  signup: (email: string, password: string) => Promise<void>;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  updateUserSettings: (settings: Partial<UserSettings>) => Promise<void>;
}

// إنشاء سياق المصادقة
export const AuthContext = React.createContext<AuthContextType>({
  currentUser: null,
  userSettings: defaultUserSettings,
  loading: true,
  error: null,
  signup: async () => {},
  login: async () => {},
  logout: async () => {},
  updateUserSettings: async () => {}
});

// مزود سياق المصادقة
export const AuthProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userSettings, setUserSettings] = useState<UserSettings>(defaultUserSettings);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // تسجيل مستخدم جديد
  const signup = async (email: string, password: string) => {
    try {
      setError(null);
      setLoading(true);
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      
      // إنشاء إعدادات المستخدم الافتراضية في Firestore
      await setDoc(doc(db, "userSettings", userCredential.user.uid), defaultUserSettings);
      
      setCurrentUser(userCredential.user);
      setUserSettings(defaultUserSettings);
    } catch (err) {
      setError("فشل في إنشاء الحساب. يرجى المحاولة مرة أخرى.");
      console.error("Error signing up:", err);
    } finally {
      setLoading(false);
    }
  };

  // تسجيل الدخول
  const login = async (email: string, password: string) => {
    try {
      setError(null);
      setLoading(true);
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      setCurrentUser(userCredential.user);
      
      // جلب إعدادات المستخدم من Firestore
      const userSettingsDoc = await getDoc(doc(db, "userSettings", userCredential.user.uid));
      
      if (userSettingsDoc.exists()) {
        setUserSettings(userSettingsDoc.data() as UserSettings);
      } else {
        // إذا لم تكن الإعدادات موجودة، استخدم الإعدادات الافتراضية
        await setDoc(doc(db, "userSettings", userCredential.user.uid), defaultUserSettings);
        setUserSettings(defaultUserSettings);
      }
    } catch (err) {
      setError("فشل في تسجيل الدخول. يرجى التحقق من بريدك الإلكتروني وكلمة المرور.");
      console.error("Error logging in:", err);
    } finally {
      setLoading(false);
    }
  };

  // تسجيل الخروج
  const logout = async () => {
    try {
      setError(null);
      await signOut(auth);
      setCurrentUser(null);
      setUserSettings(defaultUserSettings);
    } catch (err) {
      setError("فشل في تسجيل الخروج. يرجى المحاولة مرة أخرى.");
      console.error("Error logging out:", err);
    }
  };

  // تحديث إعدادات المستخدم
  const updateUserSettings = async (settings: Partial<UserSettings>) => {
    try {
      setError(null);
      
      if (!currentUser) {
        throw new Error("يجب تسجيل الدخول لتحديث الإعدادات.");
      }
      
      const updatedSettings = { ...userSettings, ...settings };
      
      // تحديث الإعدادات في Firestore
      await updateDoc(doc(db, "userSettings", currentUser.uid), updatedSettings);
      
      setUserSettings(updatedSettings);
    } catch (err) {
      setError("فشل في تحديث الإعدادات. يرجى المحاولة مرة أخرى.");
      console.error("Error updating user settings:", err);
    }
  };

  // مراقبة حالة المصادقة
  React.useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);
      
      if (user) {
        try {
          // جلب إعدادات المستخدم من Firestore
          const userSettingsDoc = await getDoc(doc(db, "userSettings", user.uid));
          
          if (userSettingsDoc.exists()) {
            setUserSettings(userSettingsDoc.data() as UserSettings);
          } else {
            // إذا لم تكن الإعدادات موجودة، استخدم الإعدادات الافتراضية
            await setDoc(doc(db, "userSettings", user.uid), defaultUserSettings);
            setUserSettings(defaultUserSettings);
          }
        } catch (err) {
          console.error("Error fetching user settings:", err);
        }
      }
      
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    userSettings,
    loading,
    error,
    signup,
    login,
    logout,
    updateUserSettings
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

// هوك استخدام المصادقة
export const useAuth = () => {
  return React.useContext(AuthContext);
};
