import React, { useState } from 'react';
import { useAuth, UserSettings } from '../services/auth';

interface UserSettingsFormProps {
  onClose: () => void;
}

const UserSettingsForm: React.FC<UserSettingsFormProps> = ({ onClose }) => {
  const { userSettings, updateUserSettings, loading, error } = useAuth();
  
  const [settings, setSettings] = useState<UserSettings>({...userSettings});
  const [saveSuccess, setSaveSuccess] = useState<boolean>(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      
      if (name.startsWith('indicator.')) {
        const indicator = name.split('.')[1];
        setSettings({
          ...settings,
          activeIndicators: {
            ...settings.activeIndicators,
            [indicator]: checkbox.checked
          }
        });
      } else {
        setSettings({
          ...settings,
          [name]: checkbox.checked
        });
      }
    } else {
      setSettings({
        ...settings,
        [name]: value
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await updateUserSettings(settings);
    
    if (!error) {
      setSaveSuccess(true);
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    }
  };

  return (
    <div className="user-settings-form card">
      <div className="card-header d-flex justify-content-between align-items-center">
        <h3>إعدادات المستخدم / User Settings</h3>
        <button 
          type="button" 
          className="btn-close" 
          aria-label="Close"
          onClick={onClose}
        ></button>
      </div>
      
      <div className="card-body">
        {error && (
          <div className="alert alert-danger" role="alert">
            {error}
          </div>
        )}
        
        {saveSuccess && (
          <div className="alert alert-success" role="alert">
            تم حفظ الإعدادات بنجاح / Settings saved successfully
          </div>
        )}
        
        <form onSubmit={handleSubmit}>
          <div className="mb-3">
            <label htmlFor="preferredLanguage" className="form-label">
              اللغة المفضلة / Preferred Language
            </label>
            <select
              className="form-select"
              id="preferredLanguage"
              name="preferredLanguage"
              value={settings.preferredLanguage}
              onChange={handleChange}
            >
              <option value="ar">العربية</option>
              <option value="en">English</option>
            </select>
          </div>
          
          <div className="mb-3">
            <label htmlFor="preferredTimeframe" className="form-label">
              الإطار الزمني المفضل / Preferred Timeframe
            </label>
            <select
              className="form-select"
              id="preferredTimeframe"
              name="preferredTimeframe"
              value={settings.preferredTimeframe}
              onChange={handleChange}
            >
              <option value="4h">4 ساعات / 4 Hours</option>
              <option value="3h">3 ساعات / 3 Hours</option>
              <option value="2h">ساعتين / 2 Hours</option>
              <option value="1d">يومي / Daily</option>
              <option value="1w">أسبوعي / Weekly</option>
            </select>
          </div>
          
          <div className="mb-3">
            <label className="form-label">
              المؤشرات الفنية النشطة / Active Technical Indicators
            </label>
            
            <div className="form-check mb-2">
              <input
                type="checkbox"
                className="form-check-input"
                id="rsiDivergence"
                name="indicator.rsiDivergence"
                checked={settings.activeIndicators.rsiDivergence}
                onChange={handleChange}
              />
              <label className="form-check-label" htmlFor="rsiDivergence">
                RSI Divergence
              </label>
            </div>
            
            <div className="form-check mb-2">
              <input
                type="checkbox"
                className="form-check-input"
                id="obvDivergence"
                name="indicator.obvDivergence"
                checked={settings.activeIndicators.obvDivergence}
                onChange={handleChange}
              />
              <label className="form-check-label" htmlFor="obvDivergence">
                OBV Divergence
              </label>
            </div>
            
            <div className="form-check mb-2">
              <input
                type="checkbox"
                className="form-check-input"
                id="macdDivergence"
                name="indicator.macdDivergence"
                checked={settings.activeIndicators.macdDivergence}
                onChange={handleChange}
              />
              <label className="form-check-label" htmlFor="macdDivergence">
                MACD Divergence
              </label>
            </div>
            
            <div className="form-check">
              <input
                type="checkbox"
                className="form-check-input"
                id="movingAverageCrossover"
                name="indicator.movingAverageCrossover"
                checked={settings.activeIndicators.movingAverageCrossover}
                onChange={handleChange}
              />
              <label className="form-check-label" htmlFor="movingAverageCrossover">
                Moving Average Crossover
              </label>
            </div>
          </div>
          
          <div className="mb-3">
            <div className="form-check">
              <input
                type="checkbox"
                className="form-check-input"
                id="notificationsEnabled"
                name="notificationsEnabled"
                checked={settings.notificationsEnabled}
                onChange={handleChange}
              />
              <label className="form-check-label" htmlFor="notificationsEnabled">
                تفعيل الإشعارات / Enable Notifications
              </label>
            </div>
          </div>
          
          <div className="d-grid gap-2">
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
              ) : (
                'حفظ الإعدادات / Save Settings'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UserSettingsForm;
