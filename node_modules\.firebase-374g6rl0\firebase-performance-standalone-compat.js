!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).firebase=t()}(this,function(){"use strict";var r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function e(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var p=function(){return(p=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function f(e,a,s,c){return new(s=s||Promise)(function(n,t){function r(e){try{o(c.next(e))}catch(e){t(e)}}function i(e){try{o(c.throw(e))}catch(e){t(e)}}function o(e){var t;e.done?n(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(r,i)}o((c=c.apply(e,a||[])).next())})}function h(n,r){var i,o,a,s={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},e={next:t(0),throw:t(1),return:t(2)};return"function"==typeof Symbol&&(e[Symbol.iterator]=function(){return this}),e;function t(t){return function(e){return function(t){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,o&&(a=2&t[0]?o.return:t[0]?o.throw||((a=o.return)&&a.call(o),0):o.next)&&!(a=a.call(o,t[1])).done)return a;switch(o=0,(t=a?[2&t[0],a.value]:t)[0]){case 0:case 1:a=t;break;case 4:return s.label++,{value:t[1],done:!1};case 5:s.label++,o=t[1],t=[0];continue;case 7:t=s.ops.pop(),s.trys.pop();continue;default:if(!(a=0<(a=s.trys).length&&a[a.length-1])&&(6===t[0]||2===t[0])){s=0;continue}if(3===t[0]&&(!a||t[1]>a[0]&&t[1]<a[3])){s.label=t[1];break}if(6===t[0]&&s.label<a[1]){s.label=a[1],a=t;break}if(a&&s.label<a[2]){s.label=a[2],s.ops.push(t);break}a[2]&&s.ops.pop(),s.trys.pop();continue}t=r.call(n,s)}catch(e){t=[6,e],o=0}finally{i=a=0}if(5&t[0])throw t[1];return{value:t[0]?t[1]:void 0,done:!0}}([t,e])}}}function d(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return{value:(e=e&&r>=e.length?void 0:e)&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function v(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(r=o.next()).done;)a.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return a}function g(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||((r=r||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))}var t,n=function(e){for(var t=[],n=0,r=0;r<e.length;r++){var i=e.charCodeAt(r);i<128?t[n++]=i:(i<2048?t[n++]=i>>6|192:(55296==(64512&i)&&r+1<e.length&&56320==(64512&e.charCodeAt(r+1))?(i=65536+((1023&i)<<10)+(1023&e.charCodeAt(++r)),t[n++]=i>>18|240,t[n++]=i>>12&63|128):t[n++]=i>>12|224,t[n++]=i>>6&63|128),t[n++]=63&i|128)}return t},i={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray:function(e,t){if(!Array.isArray(e))throw Error("encodeByteArray takes an array as a parameter");this.init_();for(var n=t?this.byteToCharMapWebSafe_:this.byteToCharMap_,r=[],i=0;i<e.length;i+=3){var o=e[i],a=i+1<e.length,s=a?e[i+1]:0,c=i+2<e.length,u=c?e[i+2]:0,l=(15&s)<<2|u>>6,u=63&u;c||(u=64,a||(l=64)),r.push(n[o>>2],n[(3&o)<<4|s>>4],n[l],n[u])}return r.join("")},encodeString:function(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(n(e),t)},decodeString:function(e,t){return this.HAS_NATIVE_SUPPORT&&!t?atob(e):function(e){for(var t=[],n=0,r=0;n<e.length;){var i,o,a,s=e[n++];s<128?t[r++]=String.fromCharCode(s):191<s&&s<224?(o=e[n++],t[r++]=String.fromCharCode((31&s)<<6|63&o)):239<s&&s<365?(i=((7&s)<<18|(63&(o=e[n++]))<<12|(63&(a=e[n++]))<<6|63&e[n++])-65536,t[r++]=String.fromCharCode(55296+(i>>10)),t[r++]=String.fromCharCode(56320+(1023&i))):(o=e[n++],a=e[n++],t[r++]=String.fromCharCode((15&s)<<12|(63&o)<<6|63&a))}return t.join("")}(this.decodeStringToByteArray(e,t))},decodeStringToByteArray:function(e,t){this.init_();for(var n=t?this.charToByteMapWebSafe_:this.charToByteMap_,r=[],i=0;i<e.length;){var o=n[e.charAt(i++)],a=i<e.length?n[e.charAt(i)]:0,s=++i<e.length?n[e.charAt(i)]:64,c=++i<e.length?n[e.charAt(i)]:64;if(++i,null==o||null==a||null==s||null==c)throw new u;r.push(o<<2|a>>4),64!==s&&(r.push(a<<4&240|s>>2),64!==c&&r.push(s<<6&192|c))}return r},init_:function(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(var e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),(this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e)>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}},u=(e(o,t=Error),o);function o(){var e=null!==t&&t.apply(this,arguments)||this;return e.name="DecodeBase64StringError",e}var a=function(e){return t=n(e),i.encodeByteArray(t,!0).replace(/\./g,"");var t};function c(e,t){if(!(t instanceof Object))return t;switch(t.constructor){case Date:return new Date(t.getTime());case Object:void 0===e&&(e={});break;case Array:e=[];break;default:return t}for(var n in t)t.hasOwnProperty(n)&&"__proto__"!==n&&(e[n]=c(e[n],t[n]));return e}function s(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")}function l(){if("undefined"!=typeof document){try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}var e=e&&function(e){try{return i.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null}(e[1]);return e&&JSON.parse(e)}}var m=function(){try{return s().__FIREBASE_DEFAULTS__||function(){if("undefined"!=typeof process&&void 0!==process.env){var e=process.env.__FIREBASE_DEFAULTS__;return e?JSON.parse(e):void 0}}()||l()}catch(e){return void console.info("Unable to get __FIREBASE_DEFAULTS__ due to: ".concat(e))}},b=function(){var e;return null===(e=m())||void 0===e?void 0:e.config},y=(_.prototype.wrapCallback=function(n){var r=this;return function(e,t){e?r.reject(e):r.resolve(t),"function"==typeof n&&(r.promise.catch(function(){}),1===n.length?n(e):n(e,t))}},_);function _(){var n=this;this.reject=function(){},this.resolve=function(){},this.promise=new Promise(function(e,t){n.resolve=e,n.reject=t})}function w(){return"undefined"!=typeof WorkerGlobalScope&&"undefined"!=typeof self&&self instanceof WorkerGlobalScope}function E(){try{return"object"==typeof indexedDB}catch(e){return}}function S(){return new Promise(function(e,t){try{var n=!0,r="validate-browser-context-for-indexeddb-analytics-module",i=self.indexedDB.open(r);i.onsuccess=function(){i.result.close(),n||self.indexedDB.deleteDatabase(r),e(!0)},i.onupgradeneeded=function(){n=!1},i.onerror=function(){var e;t((null===(e=i.error)||void 0===e?void 0:e.message)||"")}}catch(e){t(e)}})}var I,C=(e(T,I=Error),T);function T(e,t,n){var r=I.call(this,t)||this;return r.code=e,r.customData=n,r.name="FirebaseError",Object.setPrototypeOf(r,T.prototype),Error.captureStackTrace&&Error.captureStackTrace(r,A.prototype.create),r}var A=(O.prototype.create=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r,i=t[0]||{},o="".concat(this.service,"/").concat(e),a=this.errors[e],a=a?(r=i,a.replace(D,function(e,t){var n=r[t];return null!=n?String(n):"<".concat(t,"?>")})):"Error",a="".concat(this.serviceName,": ").concat(a," (").concat(o,").");return new C(o,a,i)},O);function O(e,t,n){this.service=e,this.serviceName=t,this.errors=n}var D=/\{\$([^}]+)}/g;function N(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function P(e,t){if(e===t)return 1;for(var n=Object.keys(e),r=Object.keys(t),i=0,o=n;i<o.length;i++){var a=o[i];if(!r.includes(a))return;var s=e[a],c=t[a];if(k(s)&&k(c)){if(!P(s,c))return}else if(s!==c)return}for(var u=0,l=r;u<l.length;u++){a=l[u];if(!n.includes(a))return}return 1}function k(e){return null!==e&&"object"==typeof e}function M(e,t){var n=new R(e,t);return n.subscribe.bind(n)}var R=(L.prototype.next=function(t){this.forEachObserver(function(e){e.next(t)})},L.prototype.error=function(t){this.forEachObserver(function(e){e.error(t)}),this.close(t)},L.prototype.complete=function(){this.forEachObserver(function(e){e.complete()}),this.close()},L.prototype.subscribe=function(e,t,n){var r,i=this;if(void 0===e&&void 0===t&&void 0===n)throw new Error("Missing Observer.");void 0===(r=function(e,t){if("object"!=typeof e||null===e)return!1;for(var n=0,r=t;n<r.length;n++){var i=r[n];if(i in e&&"function"==typeof e[i])return!0}return!1}(e,["next","error","complete"])?e:{next:e,error:t,complete:n}).next&&(r.next=B),void 0===r.error&&(r.error=B),void 0===r.complete&&(r.complete=B);var o=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(function(){try{i.finalError?r.error(i.finalError):r.complete()}catch(e){}}),this.observers.push(r),o},L.prototype.unsubscribeOne=function(e){void 0!==this.observers&&void 0!==this.observers[e]&&(delete this.observers[e],--this.observerCount,0===this.observerCount&&void 0!==this.onNoObservers&&this.onNoObservers(this))},L.prototype.forEachObserver=function(e){if(!this.finalized)for(var t=0;t<this.observers.length;t++)this.sendOne(t,e)},L.prototype.sendOne=function(e,t){var n=this;this.task.then(function(){if(void 0!==n.observers&&void 0!==n.observers[e])try{t(n.observers[e])}catch(e){"undefined"!=typeof console&&console.error&&console.error(e)}})},L.prototype.close=function(e){var t=this;this.finalized||(this.finalized=!0,void 0!==e&&(this.finalError=e),this.task.then(function(){t.observers=void 0,t.onNoObservers=void 0}))},L);function L(e,t){var n=this;this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=t,this.task.then(function(){e(n)}).catch(function(e){n.error(e)})}function B(){}var j=(F.prototype.setInstantiationMode=function(e){return this.instantiationMode=e,this},F.prototype.setMultipleInstances=function(e){return this.multipleInstances=e,this},F.prototype.setServiceProps=function(e){return this.serviceProps=e,this},F.prototype.setInstanceCreatedCallback=function(e){return this.onInstanceCreated=e,this},F);function F(e,t,n){this.name=e,this.instanceFactory=t,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}var x="[DEFAULT]",U=(z.prototype.get=function(e){var t=this.normalizeInstanceIdentifier(e);if(!this.instancesDeferred.has(t)){var n=new y;if(this.instancesDeferred.set(t,n),this.isInitialized(t)||this.shouldAutoInitialize())try{var r=this.getOrInitializeService({instanceIdentifier:t});r&&n.resolve(r)}catch(e){}}return this.instancesDeferred.get(t).promise},z.prototype.getImmediate=function(e){var t=this.normalizeInstanceIdentifier(null==e?void 0:e.identifier),n=null!==(n=null==e?void 0:e.optional)&&void 0!==n&&n;if(!this.isInitialized(t)&&!this.shouldAutoInitialize()){if(n)return null;throw Error("Service ".concat(this.name," is not available"))}try{return this.getOrInitializeService({instanceIdentifier:t})}catch(e){if(n)return null;throw e}},z.prototype.getComponent=function(){return this.component},z.prototype.setComponent=function(e){var t,n;if(e.name!==this.name)throw Error("Mismatching Component ".concat(e.name," for Provider ").concat(this.name,"."));if(this.component)throw Error("Component for ".concat(this.name," has already been provided"));if(this.component=e,this.shouldAutoInitialize()){if("EAGER"===e.instantiationMode)try{this.getOrInitializeService({instanceIdentifier:x})}catch(e){}try{for(var r=d(this.instancesDeferred.entries()),i=r.next();!i.done;i=r.next()){var o=v(i.value,2),a=o[0],s=o[1],c=this.normalizeInstanceIdentifier(a);try{var u=this.getOrInitializeService({instanceIdentifier:c});s.resolve(u)}catch(e){}}}catch(e){t={error:e}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(t)throw t.error}}}},z.prototype.clearInstance=function(e){this.instancesDeferred.delete(e=void 0===e?x:e),this.instancesOptions.delete(e),this.instances.delete(e)},z.prototype.delete=function(){return f(this,void 0,void 0,function(){var t;return h(this,function(e){switch(e.label){case 0:return t=Array.from(this.instances.values()),[4,Promise.all(g(g([],v(t.filter(function(e){return"INTERNAL"in e}).map(function(e){return e.INTERNAL.delete()})),!1),v(t.filter(function(e){return"_delete"in e}).map(function(e){return e._delete()})),!1))];case 1:return e.sent(),[2]}})})},z.prototype.isComponentSet=function(){return null!=this.component},z.prototype.isInitialized=function(e){return this.instances.has(e=void 0===e?x:e)},z.prototype.getOptions=function(e){return this.instancesOptions.get(e=void 0===e?x:e)||{}},z.prototype.initialize=function(e){var t,n,r=(e=void 0===e?{}:e).options,r=void 0===r?{}:r,i=this.normalizeInstanceIdentifier(e.instanceIdentifier);if(this.isInitialized(i))throw Error("".concat(this.name,"(").concat(i,") has already been initialized"));if(!this.isComponentSet())throw Error("Component ".concat(this.name," has not been registered yet"));var o=this.getOrInitializeService({instanceIdentifier:i,options:r});try{for(var a=d(this.instancesDeferred.entries()),s=a.next();!s.done;s=a.next()){var c=v(s.value,2),u=c[0],l=c[1];i===this.normalizeInstanceIdentifier(u)&&l.resolve(o)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return o},z.prototype.onInit=function(e,t){var n=this.normalizeInstanceIdentifier(t),r=null!==(i=this.onInitCallbacks.get(n))&&void 0!==i?i:new Set;r.add(e),this.onInitCallbacks.set(n,r);var i=this.instances.get(n);return i&&e(i,n),function(){r.delete(e)}},z.prototype.invokeOnInitCallbacks=function(e,t){var n,r,i=this.onInitCallbacks.get(t);if(i)try{for(var o=d(i),a=o.next();!a.done;a=o.next()){var s=a.value;try{s(e,t)}catch(e){}}}catch(e){n={error:e}}finally{try{a&&!a.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}},z.prototype.getOrInitializeService=function(e){var t=e.instanceIdentifier,n=e.options,r=void 0===n?{}:n,n=this.instances.get(t);if(!n&&this.component&&(n=this.component.instanceFactory(this.container,{instanceIdentifier:(e=t)===x?void 0:e,options:r}),this.instances.set(t,n),this.instancesOptions.set(t,r),this.invokeOnInitCallbacks(n,t),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,t,n)}catch(e){}return n||null},z.prototype.normalizeInstanceIdentifier=function(e){return void 0===e&&(e=x),!this.component||this.component.multipleInstances?e:x},z.prototype.shouldAutoInitialize=function(){return!!this.component&&"EXPLICIT"!==this.component.instantiationMode},z);function z(e,t){this.name=e,this.container=t,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}var V=(H.prototype.addComponent=function(e){var t=this.getProvider(e.name);if(t.isComponentSet())throw new Error("Component ".concat(e.name," has already been registered with ").concat(this.name));t.setComponent(e)},H.prototype.addOrOverwriteComponent=function(e){this.getProvider(e.name).isComponentSet()&&this.providers.delete(e.name),this.addComponent(e)},H.prototype.getProvider=function(e){if(this.providers.has(e))return this.providers.get(e);var t=new U(e,this);return this.providers.set(e,t),t},H.prototype.getProviders=function(){return Array.from(this.providers.values())},H);function H(e){this.name=e,this.providers=new Map}var q,W=[];(bn=q=q||{})[bn.DEBUG=0]="DEBUG",bn[bn.VERBOSE=1]="VERBOSE",bn[bn.INFO=2]="INFO",bn[bn.WARN=3]="WARN",bn[bn.ERROR=4]="ERROR",bn[bn.SILENT=5]="SILENT";function $(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];if(!(t<e.logLevel)){var i=(new Date).toISOString(),o=Y[t];if(!o)throw new Error("Attempted to log a message with an invalid logType (value: ".concat(t,")"));console[o].apply(console,g(["[".concat(i,"]  ").concat(e.name,":")],n,!1))}}var K,J={debug:q.DEBUG,verbose:q.VERBOSE,info:q.INFO,warn:q.WARN,error:q.ERROR,silent:q.SILENT},G=q.INFO,Y=((ft={})[q.DEBUG]="log",ft[q.VERBOSE]="log",ft[q.INFO]="info",ft[q.WARN]="warn",ft[q.ERROR]="error",ft),X=(Object.defineProperty(Z.prototype,"logLevel",{get:function(){return this._logLevel},set:function(e){if(!(e in q))throw new TypeError('Invalid value "'.concat(e,'" assigned to `logLevel`'));this._logLevel=e},enumerable:!1,configurable:!0}),Z.prototype.setLogLevel=function(e){this._logLevel="string"==typeof e?J[e]:e},Object.defineProperty(Z.prototype,"logHandler",{get:function(){return this._logHandler},set:function(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e},enumerable:!1,configurable:!0}),Object.defineProperty(Z.prototype,"userLogHandler",{get:function(){return this._userLogHandler},set:function(e){this._userLogHandler=e},enumerable:!1,configurable:!0}),Z.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this._userLogHandler&&this._userLogHandler.apply(this,g([this,q.DEBUG],e,!1)),this._logHandler.apply(this,g([this,q.DEBUG],e,!1))},Z.prototype.log=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this._userLogHandler&&this._userLogHandler.apply(this,g([this,q.VERBOSE],e,!1)),this._logHandler.apply(this,g([this,q.VERBOSE],e,!1))},Z.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this._userLogHandler&&this._userLogHandler.apply(this,g([this,q.INFO],e,!1)),this._logHandler.apply(this,g([this,q.INFO],e,!1))},Z.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this._userLogHandler&&this._userLogHandler.apply(this,g([this,q.WARN],e,!1)),this._logHandler.apply(this,g([this,q.WARN],e,!1))},Z.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this._userLogHandler&&this._userLogHandler.apply(this,g([this,q.ERROR],e,!1)),this._logHandler.apply(this,g([this,q.ERROR],e,!1))},Z);function Z(e){this.name=e,this._logLevel=G,this._logHandler=$,this._userLogHandler=null,W.push(this)}function Q(a,t){for(var e=0,n=W;e<n.length;e++)!function(e){var o=null;t&&t.level&&(o=J[t.level]),e.userLogHandler=null===a?null:function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i=n.map(function(e){if(null==e)return null;if("string"==typeof e)return e;if("number"==typeof e||"boolean"==typeof e)return e.toString();if(e instanceof Error)return e.message;try{return JSON.stringify(e)}catch(e){return null}}).filter(function(e){return e}).join(" ");t>=(null!=o?o:e.logLevel)&&a({level:q[t].toLowerCase(),message:i,args:n,type:e.name})}}(n[e])}const ee=(t,e)=>e.some(e=>t instanceof e);let te,ne;const re=new WeakMap,ie=new WeakMap,oe=new WeakMap,ae=new WeakMap,se=new WeakMap;let ce={get(e,t,n){if(e instanceof IDBTransaction){if("done"===t)return ie.get(e);if("objectStoreNames"===t)return e.objectStoreNames||oe.get(e);if("store"===t)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return fe(e[t])},set(e,t,n){return e[t]=n,!0},has(e,t){return e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e}};function ue(r){return r!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(ne=ne||[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey]).includes(r)?function(...e){return r.apply(pe(this),e),fe(re.get(this))}:function(...e){return fe(r.apply(pe(this),e))}:function(e,...t){var n=r.call(pe(this),e,...t);return oe.set(n,e.sort?e.sort():[e]),fe(n)}}function le(e){return"function"==typeof e?ue(e):(e instanceof IDBTransaction&&(o=e,ie.has(o)||(t=new Promise((e,t)=>{const n=()=>{o.removeEventListener("complete",r),o.removeEventListener("error",i),o.removeEventListener("abort",i)},r=()=>{e(),n()},i=()=>{t(o.error||new DOMException("AbortError","AbortError")),n()};o.addEventListener("complete",r),o.addEventListener("error",i),o.addEventListener("abort",i)}),ie.set(o,t))),ee(e,te=te||[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])?new Proxy(e,ce):e);var o,t}function fe(e){if(e instanceof IDBRequest)return function(o){const e=new Promise((e,t)=>{const n=()=>{o.removeEventListener("success",r),o.removeEventListener("error",i)},r=()=>{e(fe(o.result)),n()},i=()=>{t(o.error),n()};o.addEventListener("success",r),o.addEventListener("error",i)});return e.then(e=>{e instanceof IDBCursor&&re.set(e,o)}).catch(()=>{}),se.set(e,o),e}(e);if(ae.has(e))return ae.get(e);var t=le(e);return t!==e&&(ae.set(e,t),se.set(t,e)),t}const pe=e=>se.get(e);function he(e,t,{blocked:n,upgrade:r,blocking:i,terminated:o}={}){const a=indexedDB.open(e,t),s=fe(a);return r&&a.addEventListener("upgradeneeded",e=>{r(fe(a.result),e.oldVersion,e.newVersion,fe(a.transaction),e)}),n&&a.addEventListener("blocked",e=>n(e.oldVersion,e.newVersion,e)),s.then(e=>{o&&e.addEventListener("close",()=>o()),i&&e.addEventListener("versionchange",e=>i(e.oldVersion,e.newVersion,e))}).catch(()=>{}),s}const de=["get","getKey","getAll","getAllKeys","count"],ve=["put","add","delete","clear"],ge=new Map;function me(e,t){if(e instanceof IDBDatabase&&!(t in e)&&"string"==typeof t){if(ge.get(t))return ge.get(t);const i=t.replace(/FromIndex$/,""),o=t!==i,a=ve.includes(i);if(i in(o?IDBIndex:IDBObjectStore).prototype&&(a||de.includes(i))){var n=async function(e,...t){var n=this.transaction(e,a?"readwrite":"readonly");let r=n.store;return o&&(r=r.index(t.shift())),(await Promise.all([r[i](...t),a&&n.done]))[0]};return ge.set(t,n),n}}}ce={...K=ce,get:(e,t,n)=>me(e,t)||K.get(e,t,n),has:(e,t)=>!!me(e,t)||K.has(e,t)};var be=(ye.prototype.getPlatformInfoString=function(){return this.container.getProviders().map(function(e){if("VERSION"!==(null==(t=e.getComponent())?void 0:t.type))return null;var t,t=e.getImmediate();return"".concat(t.library,"/").concat(t.version)}).filter(function(e){return e}).join(" ")},ye);function ye(e){this.container=e}var _e="@firebase/app",we="0.10.13",Ee=new X("@firebase/app"),Se="[DEFAULT]",Ie=((Me={})[_e]="fire-core",Me["@firebase/app-compat"]="fire-core-compat",Me["@firebase/analytics"]="fire-analytics",Me["@firebase/analytics-compat"]="fire-analytics-compat",Me["@firebase/app-check"]="fire-app-check",Me["@firebase/app-check-compat"]="fire-app-check-compat",Me["@firebase/auth"]="fire-auth",Me["@firebase/auth-compat"]="fire-auth-compat",Me["@firebase/database"]="fire-rtdb",Me["@firebase/data-connect"]="fire-data-connect",Me["@firebase/database-compat"]="fire-rtdb-compat",Me["@firebase/functions"]="fire-fn",Me["@firebase/functions-compat"]="fire-fn-compat",Me["@firebase/installations"]="fire-iid",Me["@firebase/installations-compat"]="fire-iid-compat",Me["@firebase/messaging"]="fire-fcm",Me["@firebase/messaging-compat"]="fire-fcm-compat",Me["@firebase/performance"]="fire-perf",Me["@firebase/performance-compat"]="fire-perf-compat",Me["@firebase/remote-config"]="fire-rc",Me["@firebase/remote-config-compat"]="fire-rc-compat",Me["@firebase/storage"]="fire-gcs",Me["@firebase/storage-compat"]="fire-gcs-compat",Me["@firebase/firestore"]="fire-fst",Me["@firebase/firestore-compat"]="fire-fst-compat",Me["@firebase/vertexai-preview"]="fire-vertex",Me["fire-js"]="fire-js",Me.firebase="fire-js-all",Me),Ce=new Map,Te=new Map,Ae=new Map;function Oe(t,n){try{t.container.addComponent(n)}catch(e){Ee.debug("Component ".concat(n.name," failed to register with FirebaseApp ").concat(t.name),e)}}function De(e,t){e.container.addOrOverwriteComponent(t)}function Ne(e){var t,n,r,i,o=e.name;if(Ae.has(o))return Ee.debug("There were multiple attempts to register component ".concat(o,".")),!1;Ae.set(o,e);try{for(var a=d(Ce.values()),s=a.next();!s.done;s=a.next())Oe(s.value,e)}catch(e){t={error:e}}finally{try{s&&!s.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}try{for(var c=d(Te.values()),u=c.next();!u.done;u=c.next())Oe(u.value,e)}catch(e){r={error:e}}finally{try{u&&!u.done&&(i=c.return)&&i.call(c)}finally{if(r)throw r.error}}return!0}function Pe(e,t){var n=e.container.getProvider("heartbeat").getImmediate({optional:!0});return n&&n.triggerHeartbeat(),e.container.getProvider(t)}function ke(e){return void 0!==e.options}var Me=((ft={})["no-app"]="No Firebase App '{$appName}' has been created - call initializeApp() first",ft["bad-app-name"]="Illegal App name: '{$appName}'",ft["duplicate-app"]="Firebase App named '{$appName}' already exists with different options or config",ft["app-deleted"]="Firebase App named '{$appName}' already deleted",ft["server-app-deleted"]="Firebase Server App has been deleted",ft["no-options"]="Need to provide options, when not being deployed to hosting via source.",ft["invalid-app-argument"]="firebase.{$appName}() takes either no argument or a Firebase App instance.",ft["invalid-log-argument"]="First argument to `onLog` must be null or a function.",ft["idb-open"]="Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.",ft["idb-get"]="Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.",ft["idb-set"]="Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.",ft["idb-delete"]="Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.",ft["finalization-registry-not-supported"]="FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.",ft["invalid-server-app-environment"]="FirebaseServerApp is not for use in browser environments.",ft),Re=new A("app","Firebase",Me),Le=(Object.defineProperty(Be.prototype,"automaticDataCollectionEnabled",{get:function(){return this.checkDestroyed(),this._automaticDataCollectionEnabled},set:function(e){this.checkDestroyed(),this._automaticDataCollectionEnabled=e},enumerable:!1,configurable:!0}),Object.defineProperty(Be.prototype,"name",{get:function(){return this.checkDestroyed(),this._name},enumerable:!1,configurable:!0}),Object.defineProperty(Be.prototype,"options",{get:function(){return this.checkDestroyed(),this._options},enumerable:!1,configurable:!0}),Object.defineProperty(Be.prototype,"config",{get:function(){return this.checkDestroyed(),this._config},enumerable:!1,configurable:!0}),Object.defineProperty(Be.prototype,"container",{get:function(){return this._container},enumerable:!1,configurable:!0}),Object.defineProperty(Be.prototype,"isDeleted",{get:function(){return this._isDeleted},set:function(e){this._isDeleted=e},enumerable:!1,configurable:!0}),Be.prototype.checkDestroyed=function(){if(this.isDeleted)throw Re.create("app-deleted",{appName:this._name})},Be);function Be(e,t,n){var r=this;this._isDeleted=!1,this._options=p({},e),this._config=p({},t),this._name=t.name,this._automaticDataCollectionEnabled=t.automaticDataCollectionEnabled,this._container=n,this.container.addComponent(new j("app",function(){return r},"PUBLIC"))}var je,Fe=(e(xe,je=Le),xe.prototype.toJSON=function(){},Object.defineProperty(xe.prototype,"refCount",{get:function(){return this._refCount},enumerable:!1,configurable:!0}),xe.prototype.incRefCount=function(e){this.isDeleted||(this._refCount++,void 0!==e&&null!==this._finalizationRegistry&&this._finalizationRegistry.register(e,this))},xe.prototype.decRefCount=function(){return this.isDeleted?0:--this._refCount},xe.prototype.automaticCleanup=function(){Ve(this)},Object.defineProperty(xe.prototype,"settings",{get:function(){return this.checkDestroyed(),this._serverConfig},enumerable:!1,configurable:!0}),xe.prototype.checkDestroyed=function(){if(this.isDeleted)throw Re.create("server-app-deleted")},xe);function xe(e,t,n,r){var i=this,o=void 0!==t.automaticDataCollectionEnabled&&t.automaticDataCollectionEnabled,a={name:n,automaticDataCollectionEnabled:o};return(i=void 0!==e.apiKey?je.call(this,e,a,r)||this:je.call(this,e.options,a,r)||this)._serverConfig=p({automaticDataCollectionEnabled:o},t),i._finalizationRegistry=null,"undefined"!=typeof FinalizationRegistry&&(i._finalizationRegistry=new FinalizationRegistry(function(){i.automaticCleanup()})),i._refCount=0,i.incRefCount(i._serverConfig.releaseOnDeref),i._serverConfig.releaseOnDeref=void 0,t.releaseOnDeref=void 0,He(_e,we,"serverapp"),i}var Ue="10.14.1";function ze(e,t){var n,r,i=e,o=p({name:Se,automaticDataCollectionEnabled:!1},t="object"!=typeof(t=void 0===t?{}:t)?{name:t}:t),a=o.name;if("string"!=typeof a||!a)throw Re.create("bad-app-name",{appName:String(a)});if(!(i=i||b()))throw Re.create("no-options");var s=Ce.get(a);if(s){if(P(i,s.options)&&P(o,s.config))return s;throw Re.create("duplicate-app",{appName:a})}var c=new V(a);try{for(var u=d(Ae.values()),l=u.next();!l.done;l=u.next()){var f=l.value;c.addComponent(f)}}catch(e){n={error:e}}finally{try{l&&!l.done&&(r=u.return)&&r.call(u)}finally{if(n)throw n.error}}o=new Le(i,o,c);return Ce.set(a,o),o}function Ve(r){return f(this,void 0,void 0,function(){var t,n;return h(this,function(e){switch(e.label){case 0:return t=!1,n=r.name,Ce.has(n)?(t=!0,Ce.delete(n)):Te.has(n)&&r.decRefCount()<=0&&(Te.delete(n),t=!0),t?[4,Promise.all(r.container.getProviders().map(function(e){return e.delete()}))]:[3,2];case 1:e.sent(),r.isDeleted=!0,e.label=2;case 2:return[2]}})})}function He(e,t,n){var r=null!==(a=Ie[e])&&void 0!==a?a:e;n&&(r+="-".concat(n));var i=r.match(/\s|\//),o=t.match(/\s|\//);if(i||o){var a=['Unable to register library "'.concat(r,'" with version "').concat(t,'":')];return i&&a.push('library name "'.concat(r,'" contains illegal characters (whitespace or "/")')),i&&o&&a.push("and"),o&&a.push('version name "'.concat(t,'" contains illegal characters (whitespace or "/")')),void Ee.warn(a.join(" "))}Ne(new j("".concat(r,"-version"),function(){return{library:r,version:t}},"VERSION"))}function qe(e,t){if(null!==e&&"function"!=typeof e)throw Re.create("invalid-log-argument");Q(e,t)}function We(e){var t;t=e,W.forEach(function(e){e.setLogLevel(t)})}var $e="firebase-heartbeat-database",Ke=1,Je="firebase-heartbeat-store",Ge=null;function Ye(){return Ge=Ge||he($e,Ke,{upgrade:function(e,t){if(0===t)try{e.createObjectStore(Je)}catch(e){console.warn(e)}}}).catch(function(e){throw Re.create("idb-open",{originalErrorMessage:e.message})})}function Xe(r,i){return f(this,void 0,void 0,function(){var t,n;return h(this,function(e){switch(e.label){case 0:return e.trys.push([0,4,,5]),[4,Ye()];case 1:return n=e.sent(),t=n.transaction(Je,"readwrite"),[4,t.objectStore(Je).put(i,Ze(r))];case 2:return e.sent(),[4,t.done];case 3:return e.sent(),[3,5];case 4:return(n=e.sent())instanceof C?Ee.warn(n.message):(n=Re.create("idb-set",{originalErrorMessage:null==n?void 0:n.message}),Ee.warn(n.message)),[3,5];case 5:return[2]}})})}function Ze(e){return"".concat(e.name,"!").concat(e.options.appId)}var Qe=(et.prototype.triggerHeartbeat=function(){var o;return f(this,void 0,void 0,function(){var t,n,r,i;return h(this,function(e){switch(e.label){case 0:return(e.trys.push([0,3,,4]),i=this.container.getProvider("platform-logger").getImmediate(),t=i.getPlatformInfoString(),n=tt(),null!=(null===(o=this._heartbeatsCache)||void 0===o?void 0:o.heartbeats))?[3,2]:[4,(r=this)._heartbeatsCachePromise];case 1:if(r._heartbeatsCache=e.sent(),null==(null===(o=this._heartbeatsCache)||void 0===o?void 0:o.heartbeats))return[2];e.label=2;case 2:return this._heartbeatsCache.lastSentHeartbeatDate===n||this._heartbeatsCache.heartbeats.some(function(e){return e.date===n})?[2]:(this._heartbeatsCache.heartbeats.push({date:n,agent:t}),this._heartbeatsCache.heartbeats=this._heartbeatsCache.heartbeats.filter(function(e){var t=new Date(e.date).valueOf();return Date.now()-t<=2592e6}),[2,this._storage.overwrite(this._heartbeatsCache)]);case 3:return i=e.sent(),Ee.warn(i),[3,4];case 4:return[2]}})})},et.prototype.getHeartbeatsHeader=function(){var i;return f(this,void 0,void 0,function(){var t,n,r;return h(this,function(e){switch(e.label){case 0:return e.trys.push([0,6,,7]),null!==this._heartbeatsCache?[3,2]:[4,this._heartbeatsCachePromise];case 1:e.sent(),e.label=2;case 2:return null==(null===(i=this._heartbeatsCache)||void 0===i?void 0:i.heartbeats)||0===this._heartbeatsCache.heartbeats.length?[2,""]:(t=tt(),r=function(e,n){var t,r;void 0===n&&(n=1024);var i=[],o=e.slice();try{for(var a=d(e),s=a.next();!s.done;s=a.next())if("break"===function(t){var e=i.find(function(e){return e.agent===t.agent});if(e){if(e.dates.push(t.date),ot(i)>n)return e.dates.pop(),"break"}else if(i.push({agent:t.agent,dates:[t.date]}),ot(i)>n)return i.pop(),"break";o=o.slice(1)}(s.value))break}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return{heartbeatsToSend:i,unsentEntries:o}}(this._heartbeatsCache.heartbeats),n=r.heartbeatsToSend,r=r.unsentEntries,n=a(JSON.stringify({version:2,heartbeats:n})),this._heartbeatsCache.lastSentHeartbeatDate=t,0<r.length?(this._heartbeatsCache.heartbeats=r,[4,this._storage.overwrite(this._heartbeatsCache)]):[3,4]);case 3:return e.sent(),[3,5];case 4:this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache),e.label=5;case 5:return[2,n];case 6:return r=e.sent(),Ee.warn(r),[2,""];case 7:return[2]}})})},et);function et(e){var t=this;this.container=e,this._heartbeatsCache=null;var n=this.container.getProvider("app").getImmediate();this._storage=new rt(n),this._heartbeatsCachePromise=this._storage.read().then(function(e){return t._heartbeatsCache=e})}function tt(){return(new Date).toISOString().substring(0,10)}var nt,rt=(it.prototype.runIndexedDBEnvironmentCheck=function(){return f(this,void 0,void 0,function(){return h(this,function(e){return E()?[2,S().then(function(){return!0}).catch(function(){return!1})]:[2,!1]})})},it.prototype.read=function(){return f(this,void 0,void 0,function(){var t;return h(this,function(e){switch(e.label){case 0:return[4,this._canUseIndexedDBPromise];case 1:return e.sent()?[3,2]:[2,{heartbeats:[]}];case 2:return[4,function(i){return f(this,void 0,void 0,function(){var t,n,r;return h(this,function(e){switch(e.label){case 0:return e.trys.push([0,4,,5]),[4,Ye()];case 1:return r=e.sent(),[4,(t=r.transaction(Je)).objectStore(Je).get(Ze(i))];case 2:return n=e.sent(),[4,t.done];case 3:return e.sent(),[2,n];case 4:return(r=e.sent())instanceof C?Ee.warn(r.message):(r=Re.create("idb-get",{originalErrorMessage:null==r?void 0:r.message}),Ee.warn(r.message)),[3,5];case 5:return[2]}})})}(this.app)];case 3:return null!=(t=e.sent())&&t.heartbeats?[2,t]:[2,{heartbeats:[]}];case 4:return[2]}})})},it.prototype.overwrite=function(n){var r;return f(this,void 0,void 0,function(){var t;return h(this,function(e){switch(e.label){case 0:return[4,this._canUseIndexedDBPromise];case 1:return e.sent()?[3,2]:[2];case 2:return[4,this.read()];case 3:return t=e.sent(),[2,Xe(this.app,{lastSentHeartbeatDate:null!==(r=n.lastSentHeartbeatDate)&&void 0!==r?r:t.lastSentHeartbeatDate,heartbeats:n.heartbeats})]}})})},it.prototype.add=function(n){var r;return f(this,void 0,void 0,function(){var t;return h(this,function(e){switch(e.label){case 0:return[4,this._canUseIndexedDBPromise];case 1:return e.sent()?[3,2]:[2];case 2:return[4,this.read()];case 3:return t=e.sent(),[2,Xe(this.app,{lastSentHeartbeatDate:null!==(r=n.lastSentHeartbeatDate)&&void 0!==r?r:t.lastSentHeartbeatDate,heartbeats:g(g([],v(t.heartbeats),!1),v(n.heartbeats),!1)})]}})})},it);function it(e){this.app=e,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}function ot(e){return a(JSON.stringify({version:2,heartbeats:e})).length}nt="",Ne(new j("platform-logger",function(e){return new be(e)},"PRIVATE")),Ne(new j("heartbeat",function(e){return new Qe(e)},"PRIVATE")),He(_e,we,nt),He(_e,we,"esm5"),He("fire-js","");var at=Object.freeze({__proto__:null,SDK_VERSION:Ue,_DEFAULT_ENTRY_NAME:Se,_addComponent:Oe,_addOrOverwriteComponent:De,_apps:Ce,_clearComponents:function(){Ae.clear()},_components:Ae,_getProvider:Pe,_isFirebaseApp:ke,_isFirebaseServerApp:function(e){return void 0!==e.settings},_registerComponent:Ne,_removeServiceInstance:function(e,t,n){void 0===n&&(n=Se),Pe(e,t).clearInstance(n)},_serverApps:Te,deleteApp:Ve,getApp:function(e){var t=Ce.get(e=void 0===e?Se:e);if(!t&&e===Se&&b())return ze();if(!t)throw Re.create("no-app",{appName:e});return t},getApps:function(){return Array.from(Ce.values())},initializeApp:ze,initializeServerApp:function(e,t){var n,r;if(("undefined"!=typeof window||w())&&!w())throw Re.create("invalid-server-app-environment");void 0===t.automaticDataCollectionEnabled&&(t.automaticDataCollectionEnabled=!1);var i=ke(e)?e.options:e,o=p(p({},t),i);if(void 0!==o.releaseOnDeref&&delete o.releaseOnDeref,void 0!==t.releaseOnDeref&&"undefined"==typeof FinalizationRegistry)throw Re.create("finalization-registry-not-supported",{});var a=""+g([],v(JSON.stringify(o)),!1).reduce(function(e,t){return Math.imul(31,e)+t.charCodeAt(0)|0},0),o=Te.get(a);if(o)return o.incRefCount(t.releaseOnDeref),o;var s=new V(a);try{for(var c=d(Ae.values()),u=c.next();!u.done;u=c.next()){var l=u.value;s.addComponent(l)}}catch(e){n={error:e}}finally{try{u&&!u.done&&(r=c.return)&&r.call(c)}finally{if(n)throw n.error}}return i=new Fe(i,t,a,s),Te.set(a,i),i},onLog:qe,registerVersion:He,setLogLevel:We,FirebaseError:C}),st=(Object.defineProperty(ct.prototype,"automaticDataCollectionEnabled",{get:function(){return this._delegate.automaticDataCollectionEnabled},set:function(e){this._delegate.automaticDataCollectionEnabled=e},enumerable:!1,configurable:!0}),Object.defineProperty(ct.prototype,"name",{get:function(){return this._delegate.name},enumerable:!1,configurable:!0}),Object.defineProperty(ct.prototype,"options",{get:function(){return this._delegate.options},enumerable:!1,configurable:!0}),ct.prototype.delete=function(){var t=this;return new Promise(function(e){t._delegate.checkDestroyed(),e()}).then(function(){return t.firebase.INTERNAL.removeApp(t.name),Ve(t._delegate)})},ct.prototype._getService=function(e,t){var n;void 0===t&&(t=Se),this._delegate.checkDestroyed();var r=this._delegate.container.getProvider(e);return r.isInitialized()||"EXPLICIT"!==(null===(n=r.getComponent())||void 0===n?void 0:n.instantiationMode)||r.initialize(),r.getImmediate({identifier:t})},ct.prototype._removeServiceInstance=function(e,t){void 0===t&&(t=Se),this._delegate.container.getProvider(e).clearInstance(t)},ct.prototype._addComponent=function(e){Oe(this._delegate,e)},ct.prototype._addOrOverwriteComponent=function(e){De(this._delegate,e)},ct.prototype.toJSON=function(){return{name:this.name,automaticDataCollectionEnabled:this.automaticDataCollectionEnabled,options:this.options}},ct);function ct(e,t){var n=this;this._delegate=e,this.firebase=t,Oe(e,new j("app-compat",function(){return n},"PUBLIC")),this.container=e.container}var Me=((ft={})["no-app"]="No Firebase App '{$appName}' has been created - call Firebase App.initializeApp()",ft["invalid-app-argument"]="firebase.{$appName}() takes either no argument or a Firebase App instance.",ft),ut=new A("app-compat","Firebase",Me);function lt(i){var o={},a={__esModule:!0,initializeApp:function(e,t){void 0===t&&(t={});var n=ze(e,t);if(N(o,n.name))return o[n.name];var r=new i(n,a);return o[n.name]=r},app:s,registerVersion:He,setLogLevel:We,onLog:qe,apps:null,SDK_VERSION:Ue,INTERNAL:{registerComponent:function(n){var r=n.name,t=r.replace("-compat","");{var e;Ne(n)&&"PUBLIC"===n.type&&(e=function(e){if("function"!=typeof(e=void 0===e?s():e)[t])throw ut.create("invalid-app-argument",{appName:r});return e[t]()},void 0!==n.serviceProps&&c(e,n.serviceProps),a[t]=e,i.prototype[t]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return this._getService.bind(this,r).apply(this,n.multipleInstances?e:[])})}return"PUBLIC"===n.type?a[t]:null},removeApp:function(e){delete o[e]},useAsService:function(e,t){return"serverAuth"!==t?t:null},modularAPIs:at}};function s(e){if(!N(o,e=e||Se))throw ut.create("no-app",{appName:e});return o[e]}return a.default=a,Object.defineProperty(a,"apps",{get:function(){return Object.keys(o).map(function(e){return o[e]})}}),s.App=i,a}var ft=function e(){var t=lt(st);return t.INTERNAL=p(p({},t.INTERNAL),{createFirebaseNamespace:e,extendNamespace:function(e){c(t,e)},createSubscribe:M,ErrorFactory:A,deepExtend:c}),t}(),Me=new X("@firebase/app-compat");try{var pt=s();void 0!==pt.firebase&&(Me.warn("\n      Warning: Firebase is already defined in the global scope. Please make sure\n      Firebase library is only loaded once.\n    "),(dt=pt.firebase.SDK_VERSION)&&0<=dt.indexOf("LITE")&&Me.warn("\n        Warning: You are trying to load Firebase while using Firebase Performance standalone script.\n        You should load Firebase Performance with this instance of Firebase to avoid loading duplicate code.\n        "))}catch(en){}var ht=ft;He("@firebase/app-compat","0.2.43",void 0);ht.registerVersion("firebase","10.14.1","app-compat");var dt="@firebase/installations",Me="0.6.9",vt=1e4,gt="w:".concat(Me),mt="FIS_v2",bt="https://firebaseinstallations.googleapis.com/v1",yt=36e5,ft=((ft={})["missing-app-config-values"]='Missing App configuration value: "{$valueName}"',ft["not-registered"]="Firebase Installation is not registered.",ft["installation-not-found"]="Firebase Installation not found.",ft["request-failed"]='{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',ft["app-offline"]="Could not process request. Application offline.",ft["delete-pending-registration"]="Can't delete installation while there is a pending registration request.",ft),_t=new A("installations","Installations",ft);function wt(e){return e instanceof C&&e.code.includes("request-failed")}function Et(e){var t=e.projectId;return"".concat(bt,"/projects/").concat(t,"/installations")}function St(e){return{token:e.token,requestStatus:2,expiresIn:(e=e.expiresIn,Number(e.replace("s","000"))),creationTime:Date.now()}}function It(n,r){return f(this,void 0,void 0,function(){var t;return h(this,function(e){switch(e.label){case 0:return[4,r.json()];case 1:return t=e.sent(),t=t.error,[2,_t.create("request-failed",{requestName:n,serverCode:t.code,serverMessage:t.message,serverStatus:t.status})]}})})}function Ct(e){var t=e.apiKey;return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":t})}function Tt(e,t){var n=t.refreshToken,r=Ct(e);return r.append("Authorization",(e=n,"".concat(mt," ").concat(e))),r}function At(n){return f(this,void 0,void 0,function(){var t;return h(this,function(e){switch(e.label){case 0:return[4,n()];case 1:return 500<=(t=e.sent()).status&&t.status<600?[2,n()]:[2,t]}})})}function Ot(t){return new Promise(function(e){setTimeout(e,t)})}var Dt=/^[cdef][\w-]{21}$/,Nt="";function Pt(){try{var e=new Uint8Array(17);(self.crypto||self.msCrypto).getRandomValues(e),e[0]=112+e[0]%16;var t=function(e){return btoa(String.fromCharCode.apply(String,g([],v(e),!1))).replace(/\+/g,"-").replace(/\//g,"_")}(e).substr(0,22);return Dt.test(t)?t:Nt}catch(e){return Nt}}function kt(e){return"".concat(e.appName,"!").concat(e.appId)}var Mt=new Map;function Rt(e,t){var n=kt(e);Lt(n,t),function(e,t){var n=function(){!Bt&&"BroadcastChannel"in self&&((Bt=new BroadcastChannel("[Firebase] FID Change")).onmessage=function(e){Lt(e.data.key,e.data.fid)});return Bt}();n&&n.postMessage({key:e,fid:t});0===Mt.size&&Bt&&(Bt.close(),Bt=null)}(n,t)}function Lt(e,t){var n,r,i=Mt.get(e);if(i)try{for(var o=d(i),a=o.next();!a.done;a=o.next())(0,a.value)(t)}catch(e){n={error:e}}finally{try{a&&!a.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}}var Bt=null;var jt="firebase-installations-store",Ft=null;function xt(){return Ft=Ft||he("firebase-installations-database",1,{upgrade:function(e,t){0===t&&e.createObjectStore(jt)}})}function Ut(o,a){return f(this,void 0,void 0,function(){var t,n,r,i;return h(this,function(e){switch(e.label){case 0:return t=kt(o),[4,xt()];case 1:return r=e.sent(),n=r.transaction(jt,"readwrite"),[4,(r=n.objectStore(jt)).get(t)];case 2:return i=e.sent(),[4,r.put(a,t)];case 3:return e.sent(),[4,n.done];case 4:return e.sent(),i&&i.fid===a.fid||Rt(o,a.fid),[2,a]}})})}function zt(r){return f(this,void 0,void 0,function(){var t,n;return h(this,function(e){switch(e.label){case 0:return t=kt(r),[4,xt()];case 1:return n=e.sent(),[4,(n=n.transaction(jt,"readwrite")).objectStore(jt).delete(t)];case 2:return e.sent(),[4,n.done];case 3:return e.sent(),[2]}})})}function Vt(a,s){return f(this,void 0,void 0,function(){var t,n,r,i,o;return h(this,function(e){switch(e.label){case 0:return t=kt(a),[4,xt()];case 1:return r=e.sent(),n=r.transaction(jt,"readwrite"),[4,(r=n.objectStore(jt)).get(t)];case 2:return i=e.sent(),void 0!==(o=s(i))?[3,4]:[4,r.delete(t)];case 3:return e.sent(),[3,6];case 4:return[4,r.put(o,t)];case 5:e.sent(),e.label=6;case 6:return[4,n.done];case 7:return e.sent(),!o||i&&i.fid===o.fid||Rt(a,o.fid),[2,o]}})})}function Ht(i){return f(this,void 0,void 0,function(){var n,t,r;return h(this,function(e){switch(e.label){case 0:return[4,Vt(i.appConfig,function(e){var t=Wt(e||{fid:Pt(),registrationStatus:0}),t=function(e,t){{if(0!==t.registrationStatus)return 1===t.registrationStatus?{installationEntry:t,registrationPromise:function(i){return f(this,void 0,void 0,function(){var t,n,r;return h(this,function(e){switch(e.label){case 0:return[4,qt(i.appConfig)];case 1:t=e.sent(),e.label=2;case 2:return 1!==t.registrationStatus?[3,5]:[4,Ot(100)];case 3:return e.sent(),[4,qt(i.appConfig)];case 4:return t=e.sent(),[3,2];case 5:return 0!==t.registrationStatus?[3,7]:[4,Ht(i)];case 6:return r=e.sent(),n=r.installationEntry,(r=r.registrationPromise)?[2,r]:[2,n];case 7:return[2,t]}})})}(e)}:{installationEntry:t};if(!navigator.onLine){var n=Promise.reject(_t.create("app-offline"));return{installationEntry:t,registrationPromise:n}}var r={fid:t.fid,registrationStatus:1,registrationTime:Date.now()},n=function(r,i){return f(this,void 0,void 0,function(){var t,n;return h(this,function(e){switch(e.label){case 0:return e.trys.push([0,2,,7]),[4,function(e,t){var s=e.appConfig,c=e.heartbeatServiceProvider,u=t.fid;return f(this,void 0,void 0,function(){var t,n,r,i,o,a;return h(this,function(e){switch(e.label){case 0:return t=Et(s),n=Ct(s),(r=c.getImmediate({optional:!0}))?[4,r.getHeartbeatsHeader()]:[3,2];case 1:(r=e.sent())&&n.append("x-firebase-client",r),e.label=2;case 2:return a={fid:u,authVersion:mt,appId:s.appId,sdkVersion:gt},i={method:"POST",headers:n,body:JSON.stringify(a)},[4,At(function(){return fetch(t,i)})];case 3:return(o=e.sent()).ok?[4,o.json()]:[3,5];case 4:return a=e.sent(),[2,{fid:a.fid||u,registrationStatus:2,refreshToken:a.refreshToken,authToken:St(a.authToken)}];case 5:return[4,It("Create Installation",o)];case 6:throw e.sent()}})})}(r,i)];case 1:return t=e.sent(),[2,Ut(r.appConfig,t)];case 2:return wt(n=e.sent())&&409===n.customData.serverCode?[4,zt(r.appConfig)]:[3,4];case 3:return e.sent(),[3,6];case 4:return[4,Ut(r.appConfig,{fid:i.fid,registrationStatus:0})];case 5:e.sent(),e.label=6;case 6:throw n;case 7:return[2]}})})}(e,r);return{installationEntry:r,registrationPromise:n}}}(i,t);return n=t.registrationPromise,t.installationEntry})];case 1:return(t=e.sent()).fid!==Nt?[3,3]:(r={},[4,n]);case 2:return[2,(r.installationEntry=e.sent(),r)];case 3:return[2,{installationEntry:t,registrationPromise:n}]}})})}function qt(e){return Vt(e,function(e){if(!e)throw _t.create("installation-not-found");return Wt(e)})}function Wt(e){return 1===(t=e).registrationStatus&&t.registrationTime+vt<Date.now()?{fid:e.fid,registrationStatus:0}:e;var t}function $t(e,c){var u=e.appConfig,l=e.heartbeatServiceProvider;return f(this,void 0,void 0,function(){var n,r,i,o,a,s;return h(this,function(e){switch(e.label){case 0:return t=c.fid,n="".concat(Et(u),"/").concat(t,"/authTokens:generate"),r=Tt(u,c),(i=l.getImmediate({optional:!0}))?[4,i.getHeartbeatsHeader()]:[3,2];case 1:(i=e.sent())&&r.append("x-firebase-client",i),e.label=2;case 2:return s={installation:{sdkVersion:gt,appId:u.appId}},o={method:"POST",headers:r,body:JSON.stringify(s)},[4,At(function(){return fetch(n,o)})];case 3:return(a=e.sent()).ok?[4,a.json()]:[3,5];case 4:return s=e.sent(),[2,St(s)];case 5:return[4,It("Generate Auth Token",a)];case 6:throw e.sent()}var t})})}function Kt(i,o){return void 0===o&&(o=!1),f(this,void 0,void 0,function(){var r,t,n;return h(this,function(e){switch(e.label){case 0:return[4,Vt(i.appConfig,function(e){if(!Gt(e))throw _t.create("not-registered");var t,n=e.authToken;if(o||2!==(t=n).requestStatus||function(e){var t=Date.now();return t<e.creationTime||e.creationTime+e.expiresIn<t+yt}(t)){if(1===n.requestStatus)return r=function(r,i){return f(this,void 0,void 0,function(){var t,n;return h(this,function(e){switch(e.label){case 0:return[4,Jt(r.appConfig)];case 1:t=e.sent(),e.label=2;case 2:return 1!==t.authToken.requestStatus?[3,5]:[4,Ot(100)];case 3:return e.sent(),[4,Jt(r.appConfig)];case 4:return t=e.sent(),[3,2];case 5:return 0===(n=t.authToken).requestStatus?[2,Kt(r,i)]:[2,n]}})})}(i,o),e;if(!navigator.onLine)throw _t.create("app-offline");n=(t=e,n={requestStatus:1,requestTime:Date.now()},p(p({},t),{authToken:n}));return r=function(i,o){return f(this,void 0,void 0,function(){var t,n,r;return h(this,function(e){switch(e.label){case 0:return e.trys.push([0,3,,8]),[4,$t(i,o)];case 1:return t=e.sent(),r=p(p({},o),{authToken:t}),[4,Ut(i.appConfig,r)];case 2:return e.sent(),[2,t];case 3:return!wt(n=e.sent())||401!==n.customData.serverCode&&404!==n.customData.serverCode?[3,5]:[4,zt(i.appConfig)];case 4:return e.sent(),[3,7];case 5:return r=p(p({},o),{authToken:{requestStatus:0}}),[4,Ut(i.appConfig,r)];case 6:e.sent(),e.label=7;case 7:throw n;case 8:return[2]}})})}(i,n),n}return e})];case 1:return t=e.sent(),r?[4,r]:[3,3];case 2:return n=e.sent(),[3,4];case 3:n=t.authToken,e.label=4;case 4:return[2,n]}})})}function Jt(e){return Vt(e,function(e){if(!Gt(e))throw _t.create("not-registered");var t,n=e.authToken;return 1===(t=n).requestStatus&&t.requestTime+vt<Date.now()?p(p({},e),{authToken:{requestStatus:0}}):e})}function Gt(e){return void 0!==e&&2===e.registrationStatus}function Yt(n,r){return void 0===r&&(r=!1),f(this,void 0,void 0,function(){var t;return h(this,function(e){switch(e.label){case 0:return[4,function(n){return f(this,void 0,void 0,function(){var t;return h(this,function(e){switch(e.label){case 0:return[4,Ht(n)];case 1:return(t=e.sent().registrationPromise)?[4,t]:[3,3];case 2:e.sent(),e.label=3;case 3:return[2]}})})}(t=n)];case 1:return e.sent(),[4,Kt(t,r)];case 2:return[2,e.sent().token]}})})}function Xt(e){return _t.create("missing-app-config-values",{valueName:e})}function Zt(e){var t=Pe(e.getProvider("app").getImmediate(),Qt).getImmediate();return{getId:function(){return function(i){return f(this,void 0,void 0,function(){var t,n,r;return h(this,function(e){switch(e.label){case 0:return[4,Ht(t=i)];case 1:return n=e.sent(),r=n.installationEntry,(n.registrationPromise||Kt(t)).catch(console.error),[2,r.fid]}})})}(t)},getToken:function(e){return Yt(t,e)}}}var Qt="installations";Ne(new j(Qt,function(e){var t=e.getProvider("app").getImmediate();return{app:t,appConfig:function(e){var t,n;if(!e||!e.options)throw Xt("App Configuration");if(!e.name)throw Xt("App Name");try{for(var r=d(["projectId","apiKey","appId"]),i=r.next();!i.done;i=r.next()){var o=i.value;if(!e.options[o])throw Xt(o)}}catch(e){t={error:e}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(t)throw t.error}}return{appName:e.name,projectId:e.options.projectId,apiKey:e.options.apiKey,appId:e.options.appId}}(t),heartbeatServiceProvider:Pe(t,"heartbeat"),_delete:function(){return Promise.resolve()}}},"PUBLIC")),Ne(new j("installations-internal",Zt,"PRIVATE")),He(dt,Me),He(dt,Me,"esm5");var en,tn,nn,rn="@firebase/performance",on="0.6.9",an=on,sn="FB-PERF-TRACE-MEASURE",cn="@firebase/performance/config",un="@firebase/performance/configexpire",dt="Performance",Me=((en={})["trace started"]="Trace {$traceName} was started before.",en["trace stopped"]="Trace {$traceName} is not running.",en["nonpositive trace startTime"]="Trace {$traceName} startTime should be positive.",en["nonpositive trace duration"]="Trace {$traceName} duration should be positive.",en["no window"]="Window is not available.",en["no app id"]="App id is not available.",en["no project id"]="Project id is not available.",en["no api key"]="Api key is not available.",en["invalid cc log"]="Attempted to queue invalid cc event",en["FB not default"]="Performance can only start when Firebase app instance is the default one.",en["RC response not ok"]="RC response is not ok",en["invalid attribute name"]="Attribute name {$attributeName} is invalid.",en["invalid attribute value"]="Attribute value {$attributeValue} is invalid.",en["invalid custom metric name"]="Custom metric name {$customMetricName} is invalid",en["invalid String merger input"]="Input for String merger is invalid, contact support team to resolve.",en["already initialized"]="initializePerformance() has already been called with different options. To avoid this error, call initializePerformance() with the same options as when it was originally called, or call getPerformance() to return the already initialized instance.",en),ln=new A("performance",dt,Me),fn=new X(dt);fn.logLevel=q.INFO;var pn,hn,dn=(vn.prototype.getUrl=function(){return this.windowLocation.href.split("?")[0]},vn.prototype.mark=function(e){this.performance&&this.performance.mark&&this.performance.mark(e)},vn.prototype.measure=function(e,t,n){this.performance&&this.performance.measure&&this.performance.measure(e,t,n)},vn.prototype.getEntriesByType=function(e){return this.performance&&this.performance.getEntriesByType?this.performance.getEntriesByType(e):[]},vn.prototype.getEntriesByName=function(e){return this.performance&&this.performance.getEntriesByName?this.performance.getEntriesByName(e):[]},vn.prototype.getTimeOrigin=function(){return this.performance&&(this.performance.timeOrigin||this.performance.timing.navigationStart)},vn.prototype.requiredApisAvailable=function(){return fetch&&Promise&&"undefined"!=typeof navigator&&navigator.cookieEnabled?!!E()||(fn.info("IndexedDB is not supported by current browser"),!1):(fn.info("Firebase Performance cannot start if browser does not support fetch and Promise or cookie is disabled."),!1)},vn.prototype.setupObserver=function(e,i){this.PerformanceObserver&&new this.PerformanceObserver(function(e){for(var t=0,n=e.getEntries();t<n.length;t++){var r=n[t];i(r)}}).observe({entryTypes:[e]})},vn.getInstance=function(){return tn=void 0===tn?new vn(nn):tn},vn);function vn(e){if(!(this.window=e))throw ln.create("no window");this.performance=e.performance,this.PerformanceObserver=e.PerformanceObserver,this.windowLocation=e.location,this.navigator=e.navigator,this.document=e.document,this.navigator&&this.navigator.cookieEnabled&&(this.localStorage=e.localStorage),e.perfMetrics&&e.perfMetrics.onFirstInputDelay&&(this.onFirstInputDelay=e.perfMetrics.onFirstInputDelay)}function gn(e,t){var n=e.length-t.length;if(n<0||1<n)throw ln.create("invalid String merger input");for(var r=[],i=0;i<e.length;i++)r.push(e.charAt(i)),t.length>i&&r.push(t.charAt(i));return r.join("")}var mn,bn,yn=(_n.prototype.getFlTransportFullUrl=function(){return this.flTransportEndpointUrl.concat("?key=",this.transportKey)},_n.getInstance=function(){return hn=void 0===hn?new _n:hn},_n);function _n(){this.instrumentationEnabled=!0,this.dataCollectionEnabled=!0,this.loggingEnabled=!1,this.tracesSamplingRate=1,this.networkRequestsSamplingRate=1,this.logEndPointUrl="https://firebaselogging.googleapis.com/v0cc/log?format=json_proto",this.flTransportEndpointUrl=gn("hts/frbslgigp.ogepscmv/ieo/eaylg","tp:/ieaeogn-agolai.o/1frlglgc/o"),this.transportKey=gn("AzSC8r6ReiGqFMyfvgow","Iayx0u-XT3vksVM-pIV"),this.logSource=462,this.logTraceAfterSampling=!1,this.logNetworkAfterSampling=!1,this.configTimeToLive=12}(bn=mn=mn||{})[bn.UNKNOWN=0]="UNKNOWN",bn[bn.VISIBLE=1]="VISIBLE",bn[bn.HIDDEN=2]="HIDDEN";var wn=["firebase_","google_","ga_"],En=new RegExp("^[a-zA-Z]\\w*$");function Sn(){switch(dn.getInstance().document.visibilityState){case"visible":return mn.VISIBLE;case"hidden":return mn.HIDDEN;default:return mn.UNKNOWN}}function In(e){var t=null===(t=e.options)||void 0===t?void 0:t.appId;if(!t)throw ln.create("no app id");return t}var Cn="0.0.1",Tn={loggingEnabled:!0},An="FIREBASE_INSTALLATIONS_AUTH";function On(e,t){var r,i,n=function(){var e=dn.getInstance().localStorage;if(e){var t=e.getItem(un);if(t&&function(e){return Number(e)>Date.now()}(t)){e=e.getItem(cn);if(e)try{return JSON.parse(e)}catch(e){return}}}}();return n?(Nn(n),Promise.resolve()):(i=t,function(e){var t=e.getToken();return t.then(function(e){}),t}((r=e).installations).then(function(e){var t=function(e){var t=null===(t=e.options)||void 0===t?void 0:t.projectId;if(!t)throw ln.create("no project id");return t}(r.app),n=function(e){var t=null===(t=e.options)||void 0===t?void 0:t.apiKey;if(!t)throw ln.create("no api key");return t}(r.app),n="https://firebaseremoteconfig.googleapis.com/v1/projects/".concat(t,"/namespaces/fireperf:fetch?key=").concat(n),n=new Request(n,{method:"POST",headers:{Authorization:"".concat(An," ").concat(e)},body:JSON.stringify({app_instance_id:i,app_instance_id_token:e,app_id:In(r.app),app_version:an,sdk_version:Cn})});return fetch(n).then(function(e){if(e.ok)return e.json();throw ln.create("RC response not ok")})}).catch(function(){fn.info(Dn)}).then(Nn).then(function(e){var t;e=e,t=dn.getInstance().localStorage,e&&t&&(t.setItem(cn,JSON.stringify(e)),t.setItem(un,String(Date.now()+60*yn.getInstance().configTimeToLive*60*1e3)))},function(){}))}var Dn="Could not fetch config, will use default configs";function Nn(e){if(!e)return e;var t=yn.getInstance(),n=e.entries||{};return void 0!==n.fpr_enabled?t.loggingEnabled="true"===String(n.fpr_enabled):t.loggingEnabled=Tn.loggingEnabled,n.fpr_log_source?t.logSource=Number(n.fpr_log_source):Tn.logSource&&(t.logSource=Tn.logSource),n.fpr_log_endpoint_url?t.logEndPointUrl=n.fpr_log_endpoint_url:Tn.logEndPointUrl&&(t.logEndPointUrl=Tn.logEndPointUrl),n.fpr_log_transport_key?t.transportKey=n.fpr_log_transport_key:Tn.transportKey&&(t.transportKey=Tn.transportKey),void 0!==n.fpr_vc_network_request_sampling_rate?t.networkRequestsSamplingRate=Number(n.fpr_vc_network_request_sampling_rate):void 0!==Tn.networkRequestsSamplingRate&&(t.networkRequestsSamplingRate=Tn.networkRequestsSamplingRate),void 0!==n.fpr_vc_trace_sampling_rate?t.tracesSamplingRate=Number(n.fpr_vc_trace_sampling_rate):void 0!==Tn.tracesSamplingRate&&(t.tracesSamplingRate=Tn.tracesSamplingRate),t.logTraceAfterSampling=Pn(t.tracesSamplingRate),t.logNetworkAfterSampling=Pn(t.networkRequestsSamplingRate),e}function Pn(e){return Math.random()<=e}var kn,Mn=1;function Rn(e){var n;return Mn=2,kn=kn||(n=e,function(){var n=dn.getInstance().document;return new Promise(function(e){var t;n&&"complete"!==n.readyState?(t=function(){"complete"===n.readyState&&(n.removeEventListener("readystatechange",t),e())},n.addEventListener("readystatechange",t)):e()})}().then(function(){return e=n.installations,(t=e.getId()).then(function(e){pn=e}),t;var e,t}).then(function(e){return On(n,e)}).then(Ln,Ln))}function Ln(){Mn=3}var Bn,jn=1e4,Fn=3,xn=1e3,Un=Fn,zn=[],Vn=!1;function Hn(e){setTimeout(function(){var e,t;if(0!==Un)return zn.length?(e=zn.splice(0,xn),t=e.map(function(e){return{source_extension_json_proto3:e.message,event_time_ms:String(e.eventTime)}}),void function(e,r){return function(e){var t=yn.getInstance().getFlTransportFullUrl();return fetch(t,{method:"POST",body:JSON.stringify(e)})}(e).then(function(e){return e.ok||fn.info("Call to Firebase backend failed."),e.json()}).then(function(e){var t=Number(e.nextRequestWaitMillis),n=jn;isNaN(t)||(n=Math.max(t,n));t=e.logResponseDetails;Array.isArray(t)&&0<t.length&&"RETRY_REQUEST_LATER"===t[0].responseAction&&(zn=g(g([],r,!0),zn,!0),fn.info("Retry transport request later.")),Un=Fn,Hn(n)})}({request_time_ms:String(Date.now()),client_info:{client_type:1,js_client_info:{}},log_source:yn.getInstance().logSource,log_event:t},e).catch(function(){zn=g(g([],e,!0),zn,!0),Un--,fn.info("Tries left: ".concat(Un,".")),Hn(jn)})):Hn(jn)},e)}function qn(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];!function(e){if(!e.eventTime||!e.message)throw ln.create("invalid cc log");zn=g(g([],zn,!0),[e],!1)}({message:n.apply(void 0,e),eventTime:Date.now()})}}function Wn(e,t){(Bn=Bn||qn(Jn))(e,t)}function $n(e){var t=yn.getInstance();!t.instrumentationEnabled&&e.isAuto||(t.dataCollectionEnabled||e.isAuto)&&dn.getInstance().requiredApisAvailable()&&(e.isAuto&&Sn()!==mn.VISIBLE||(3===Mn?Kn(e):Rn(e.performanceController).then(function(){return Kn(e)},function(){return Kn(e)})))}function Kn(e){var t;!pn||(t=yn.getInstance()).loggingEnabled&&t.logTraceAfterSampling&&setTimeout(function(){return Wn(e,1)},0)}function Jn(e,t){return 0===t?(n={url:e.url,http_method:e.httpMethod||0,http_response_code:200,response_payload_bytes:e.responsePayloadBytes,client_start_time_us:e.startTimeUs,time_to_response_initiated_us:e.timeToResponseInitiatedUs,time_to_response_completed_us:e.timeToResponseCompletedUs},n={application_info:Gn(e.performanceController.app),network_request_metric:n},JSON.stringify(n)):function(e){var t={name:e.name,is_auto:e.isAuto,client_start_time_us:e.startTimeUs,duration_us:e.durationUs};0!==Object.keys(e.counters).length&&(t.counters=e.counters);var n=e.getAttributes();0!==Object.keys(n).length&&(t.custom_attributes=n);t={application_info:Gn(e.performanceController.app),trace_metric:t};return JSON.stringify(t)}(e);var n}function Gn(e){return{google_app_id:In(e),app_instance_id:pn,web_app_info:{sdk_version:an,page_url:dn.getInstance().getUrl(),service_worker_status:null!=(t=dn.getInstance().navigator)&&t.serviceWorker?t.serviceWorker.controller?2:3:1,visibility_state:Sn(),effective_connection_type:function(){var e=dn.getInstance().navigator.connection;switch(e&&e.effectiveType){case"slow-2g":return 1;case"2g":return 2;case"3g":return 3;case"4g":return 4;default:return 0}}()},application_process_state:0};var t}var Yn=["_fp","_fcp","_fid"];var Xn=(Zn.prototype.start=function(){if(1!==this.state)throw ln.create("trace started",{traceName:this.name});this.api.mark(this.traceStartMark),this.state=2},Zn.prototype.stop=function(){if(2!==this.state)throw ln.create("trace stopped",{traceName:this.name});this.state=3,this.api.mark(this.traceStopMark),this.api.measure(this.traceMeasure,this.traceStartMark,this.traceStopMark),this.calculateTraceMetrics(),$n(this)},Zn.prototype.record=function(e,t,n){if(e<=0)throw ln.create("nonpositive trace startTime",{traceName:this.name});if(t<=0)throw ln.create("nonpositive trace duration",{traceName:this.name});if(this.durationUs=Math.floor(1e3*t),this.startTimeUs=Math.floor(1e3*e),n&&n.attributes&&(this.customAttributes=p({},n.attributes)),n&&n.metrics)for(var r=0,i=Object.keys(n.metrics);r<i.length;r++){var o=i[r];isNaN(Number(n.metrics[o]))||(this.counters[o]=Math.floor(Number(n.metrics[o])))}$n(this)},Zn.prototype.incrementMetric=function(e,t){void 0===t&&(t=1),void 0===this.counters[e]?this.putMetric(e,t):this.putMetric(e,this.counters[e]+t)},Zn.prototype.putMetric=function(e,t){if(r=e,i=this.name,0===r.length||100<r.length||!(i&&i.startsWith("_wt_")&&-1<Yn.indexOf(r))&&r.startsWith("_"))throw ln.create("invalid custom metric name",{customMetricName:e});var n,r,i;this.counters[e]=(t=null!=t?t:0,(n=Math.floor(t))<t&&fn.info("Metric value should be an Integer, setting the value as : ".concat(n,".")),n)},Zn.prototype.getMetric=function(e){return this.counters[e]||0},Zn.prototype.putAttribute=function(e,t){var n,r,i=!(0===(n=e).length||40<n.length)&&(!wn.some(function(e){return n.startsWith(e)})&&!!n.match(En)),o=0!==(r=t).length&&r.length<=100;if(i&&o)this.customAttributes[e]=t;else{if(!i)throw ln.create("invalid attribute name",{attributeName:e});if(!o)throw ln.create("invalid attribute value",{attributeValue:t})}},Zn.prototype.getAttribute=function(e){return this.customAttributes[e]},Zn.prototype.removeAttribute=function(e){void 0!==this.customAttributes[e]&&delete this.customAttributes[e]},Zn.prototype.getAttributes=function(){return p({},this.customAttributes)},Zn.prototype.setStartTime=function(e){this.startTimeUs=e},Zn.prototype.setDuration=function(e){this.durationUs=e},Zn.prototype.calculateTraceMetrics=function(){var e=this.api.getEntriesByName(this.traceMeasure),e=e&&e[0];e&&(this.durationUs=Math.floor(1e3*e.duration),this.startTimeUs=Math.floor(1e3*(e.startTime+this.api.getTimeOrigin())))},Zn.createOobTrace=function(e,t,n,r){var i,o=dn.getInstance().getUrl();o&&(i=new Zn(e,"_wt_"+o,!0),o=Math.floor(1e3*dn.getInstance().getTimeOrigin()),i.setStartTime(o),t&&t[0]&&(i.setDuration(Math.floor(1e3*t[0].duration)),i.putMetric("domInteractive",Math.floor(1e3*t[0].domInteractive)),i.putMetric("domContentLoadedEventEnd",Math.floor(1e3*t[0].domContentLoadedEventEnd)),i.putMetric("loadEventEnd",Math.floor(1e3*t[0].loadEventEnd))),n&&((o=n.find(function(e){return"first-paint"===e.name}))&&o.startTime&&i.putMetric("_fp",Math.floor(1e3*o.startTime)),(o=n.find(function(e){return"first-contentful-paint"===e.name}))&&o.startTime&&i.putMetric("_fcp",Math.floor(1e3*o.startTime)),r&&i.putMetric("_fid",Math.floor(1e3*r))),$n(i))},Zn.createUserTimingTrace=function(e,t){$n(new Zn(e,t,!1,t))},Zn);function Zn(e,t,n,r){void 0===n&&(n=!1),this.performanceController=e,this.name=t,this.isAuto=n,this.state=1,this.customAttributes={},this.counters={},this.api=dn.getInstance(),this.randomId=Math.floor(1e6*Math.random()),this.isAuto||(this.traceStartMark="".concat("FB-PERF-TRACE-START","-").concat(this.randomId,"-").concat(this.name),this.traceStopMark="".concat("FB-PERF-TRACE-STOP","-").concat(this.randomId,"-").concat(this.name),this.traceMeasure=r||"".concat(sn,"-").concat(this.randomId,"-").concat(this.name),r&&this.calculateTraceMetrics())}function Qn(e,t){var n,r,i,o,a=t;a&&void 0!==a.responseStart&&(i=dn.getInstance().getTimeOrigin(),o=Math.floor(1e3*(a.startTime+i)),r=a.responseStart?Math.floor(1e3*(a.responseStart-a.startTime)):void 0,i=Math.floor(1e3*(a.responseEnd-a.startTime)),a={performanceController:e,url:a.name&&a.name.split("?")[0],responsePayloadBytes:a.transferSize,startTimeUs:o,timeToResponseInitiatedUs:r,timeToResponseCompletedUs:i},n=a,(o=yn.getInstance()).instrumentationEnabled&&(r=n.url,i=o.logEndPointUrl.split("?")[0],a=o.flTransportEndpointUrl.split("?")[0],r!==i&&r!==a&&o.loggingEnabled&&o.logNetworkAfterSampling&&setTimeout(function(){return Wn(n,0)},0)))}var er=5e3;function tr(e){pn&&(setTimeout(function(){return function(t){var e=dn.getInstance(),n=e.getEntriesByType("navigation"),r=e.getEntriesByType("paint");{var i;e.onFirstInputDelay?(i=setTimeout(function(){Xn.createOobTrace(t,n,r),i=void 0},er),e.onFirstInputDelay(function(e){i&&(clearTimeout(i),Xn.createOobTrace(t,n,r,e))})):Xn.createOobTrace(t,n,r)}}(e)},0),setTimeout(function(){return function(t){for(var e=dn.getInstance(),n=e.getEntriesByType("resource"),r=0,i=n;r<i.length;r++){var o=i[r];Qn(t,o)}e.setupObserver("resource",function(e){return Qn(t,e)})}(e)},0),setTimeout(function(){return function(t){for(var e=dn.getInstance(),n=e.getEntriesByType("measure"),r=0,i=n;r<i.length;r++){var o=i[r];nr(t,o)}e.setupObserver("measure",function(e){return nr(t,e)})}(e)},0))}function nr(e,t){var n=t.name;n.substring(0,sn.length)!==sn&&Xn.createUserTimingTrace(e,n)}var rr=(ir.prototype._init=function(e){var t=this;this.initialized||(void 0!==(null==e?void 0:e.dataCollectionEnabled)&&(this.dataCollectionEnabled=e.dataCollectionEnabled),void 0!==(null==e?void 0:e.instrumentationEnabled)&&(this.instrumentationEnabled=e.instrumentationEnabled),dn.getInstance().requiredApisAvailable()?S().then(function(e){e&&(Vn||(Hn(5500),Vn=!0),Rn(t).then(function(){return tr(t)},function(){return tr(t)}),t.initialized=!0)}).catch(function(e){fn.info("Environment doesn't support IndexedDB: ".concat(e))}):fn.info('Firebase Performance cannot start if the browser does not support "Fetch" and "Promise", or cookies are disabled.'))},Object.defineProperty(ir.prototype,"instrumentationEnabled",{get:function(){return yn.getInstance().instrumentationEnabled},set:function(e){yn.getInstance().instrumentationEnabled=e},enumerable:!1,configurable:!0}),Object.defineProperty(ir.prototype,"dataCollectionEnabled",{get:function(){return yn.getInstance().dataCollectionEnabled},set:function(e){yn.getInstance().dataCollectionEnabled=e},enumerable:!1,configurable:!0}),ir);function ir(e,t){this.app=e,this.installations=t,this.initialized=!1}var or="[DEFAULT]";Ne(new j("performance",function(e,t){var n=t.options,r=e.getProvider("app").getImmediate(),i=e.getProvider("installations-internal").getImmediate();if(r.name!==or)throw ln.create("FB not default");if("undefined"==typeof window)throw ln.create("no window");nn=window;i=new rr(r,i);return i._init(n),i},"PUBLIC")),He(rn,on),He(rn,on,"esm5");var ar=(Object.defineProperty(sr.prototype,"instrumentationEnabled",{get:function(){return this._delegate.instrumentationEnabled},set:function(e){this._delegate.instrumentationEnabled=e},enumerable:!1,configurable:!0}),Object.defineProperty(sr.prototype,"dataCollectionEnabled",{get:function(){return this._delegate.dataCollectionEnabled},set:function(e){this._delegate.dataCollectionEnabled=e},enumerable:!1,configurable:!0}),sr.prototype.trace=function(e){return t=this._delegate,t=(n=t)&&n._delegate?n._delegate:n,new Xn(t,e);var t,n},sr);function sr(e,t){this.app=e,this._delegate=t}var cr;function ur(e){var t=e.getProvider("app-compat").getImmediate(),n=e.getProvider("performance").getImmediate();return new ar(t,n)}(cr=ht).INTERNAL.registerComponent(new j("performance-compat",ur,"PUBLIC")),cr.registerVersion("@firebase/performance-compat","0.2.9");return ht.registerVersion("firebase","10.14.1","compat-lite"),ht});
//# sourceMappingURL=firebase-performance-standalone-compat.js.map
