// خدمة الإشعارات لتطبيق تحليل العملات الرقمية

import { getMessaging, getToken, onMessage } from 'firebase/messaging';
import { getFirestore, doc, setDoc, collection, addDoc } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { Signal } from './technicalAnalysis';

// واجهة إشعار
export interface Notification {
  id: string;
  userId: string;
  title: string;
  body: string;
  timestamp: string;
  read: boolean;
  signalId?: string;
  signalType?: 'buy' | 'sell';
}

// تهيئة خدمة الإشعارات
let messaging: any = null;
let notificationsEnabled = false;

// تهيئة Firebase Messaging
export const initializeNotifications = async () => {
  try {
    const firebaseApp = getAuth().app;
    messaging = getMessaging(firebaseApp);
    
    // طلب إذن الإشعارات
    const permission = await Notification.requestPermission();
    
    if (permission === 'granted') {
      notificationsEnabled = true;
      
      // الحصول على رمز الجهاز
      const token = await getToken(messaging, {
        vapidKey: 'YOUR_VAPID_KEY' // يجب استبداله بمفتاح VAPID الخاص بك
      });
      
      // تخزين رمز الجهاز في Firestore
      const auth = getAuth();
      if (auth.currentUser) {
        const db = getFirestore();
        await setDoc(doc(db, 'userTokens', auth.currentUser.uid), {
          token,
          lastUpdated: new Date().toISOString()
        });
      }
      
      // الاستماع للإشعارات الواردة
      onMessage(messaging, (payload) => {
        console.log('تم استلام إشعار:', payload);
        
        // إظهار الإشعار محلياً إذا كان التطبيق مفتوحاً
        if (payload.notification) {
          const { title, body } = payload.notification;
          showLocalNotification(title || 'إشعار جديد', body || '');
        }
      });
      
      return true;
    } else {
      console.log('تم رفض إذن الإشعارات');
      notificationsEnabled = false;
      return false;
    }
  } catch (error) {
    console.error('خطأ في تهيئة الإشعارات:', error);
    notificationsEnabled = false;
    return false;
  }
};

// إرسال إشعار للمستخدم عند ظهور إشارة جديدة
export const sendSignalNotification = async (signal: Signal, language: string = 'ar') => {
  try {
    const auth = getAuth();
    if (!auth.currentUser || !notificationsEnabled) {
      return false;
    }
    
    const db = getFirestore();
    
    // إنشاء إشعار في Firestore
    const notification: Notification = {
      id: `notification-${Date.now()}`,
      userId: auth.currentUser.uid,
      title: language === 'ar' 
        ? `إشارة ${signal.type === 'buy' ? 'شراء' : 'بيع'} جديدة`
        : `New ${signal.type === 'buy' ? 'Buy' : 'Sell'} Signal`,
      body: language === 'ar'
        ? `${signal.reason} - السعر: ${signal.price}`
        : `${signal.reason} - Price: ${signal.price}`,
      timestamp: new Date().toISOString(),
      read: false,
      signalId: signal.id,
      signalType: signal.type
    };
    
    // إضافة الإشعار إلى مجموعة إشعارات المستخدم
    await addDoc(collection(db, 'users', auth.currentUser.uid, 'notifications'), notification);
    
    // إظهار الإشعار محلياً إذا كان التطبيق مفتوحاً
    showLocalNotification(notification.title, notification.body);
    
    return true;
  } catch (error) {
    console.error('خطأ في إرسال الإشعار:', error);
    return false;
  }
};

// إظهار إشعار محلي
const showLocalNotification = (title: string, body: string) => {
  // التحقق من دعم الإشعارات في المتصفح
  if (!('Notification' in window)) {
    console.log('هذا المتصفح لا يدعم إشعارات سطح المكتب');
    return;
  }
  
  // إظهار الإشعار
  if (Notification.permission === 'granted') {
    const notification = new Notification(title, {
      body,
      icon: '/logo192.png' // يمكن تغييره إلى شعار التطبيق
    });
    
    // إضافة معالج النقر على الإشعار
    notification.onclick = () => {
      window.focus();
      notification.close();
    };
  }
};

// الحصول على إشعارات المستخدم
export const getUserNotifications = async (): Promise<Notification[]> => {
  try {
    const auth = getAuth();
    if (!auth.currentUser) {
      return [];
    }
    
    const db = getFirestore();
    const notificationsSnapshot = await collection(db, 'users', auth.currentUser.uid, 'notifications').get();
    
    const notifications: Notification[] = [];
    notificationsSnapshot.forEach((doc) => {
      notifications.push(doc.data() as Notification);
    });
    
    // ترتيب الإشعارات حسب التاريخ (الأحدث أولاً)
    return notifications.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  } catch (error) {
    console.error('خطأ في جلب الإشعارات:', error);
    return [];
  }
};

// تحديث حالة قراءة الإشعار
export const markNotificationAsRead = async (notificationId: string): Promise<boolean> => {
  try {
    const auth = getAuth();
    if (!auth.currentUser) {
      return false;
    }
    
    const db = getFirestore();
    await setDoc(
      doc(db, 'users', auth.currentUser.uid, 'notifications', notificationId),
      { read: true },
      { merge: true }
    );
    
    return true;
  } catch (error) {
    console.error('خطأ في تحديث حالة الإشعار:', error);
    return false;
  }
};

// التحقق من حالة تفعيل الإشعارات
export const isNotificationsEnabled = (): boolean => {
  return notificationsEnabled;
};
