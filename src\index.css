/* Bootstrap will be added later */

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* RTL support for Arabic */
html[dir="rtl"] .ms-auto {
  margin-right: auto !important;
  margin-left: 0 !important;
}

html[dir="rtl"] .me-auto {
  margin-left: auto !important;
  margin-right: 0 !important;
}

/* Custom styles for crypto app */
.chart-container {
  height: 500px;
  width: 100%;
}

.timeframe-selector {
  margin-bottom: 1rem;
}

.signal-indicator {
  position: absolute;
  z-index: 100;
}

.signal-buy {
  color: #28a745;
}

.signal-sell {
  color: #dc3545;
}

.language-selector {
  position: absolute;
  top: 10px;
  right: 10px;
}

html[dir="rtl"] .language-selector {
  right: auto;
  left: 10px;
}
