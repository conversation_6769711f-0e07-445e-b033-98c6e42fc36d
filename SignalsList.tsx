import React from 'react';

interface SignalProps {
  signals: Array<{
    id: string;
    type: 'buy' | 'sell';
    reason: string;
    indicator: string;
    timestamp: string;
    price: number;
  }>;
}

const SignalsList: React.FC<SignalProps> = ({ signals }) => {
  return (
    <div className="signals-list card mb-4">
      <div className="card-header d-flex justify-content-between align-items-center">
        <span>إشارات البيع والشراء / Trading Signals</span>
        <span className="badge bg-primary">{signals.length}</span>
      </div>
      <div className="card-body p-0">
        <div className="list-group list-group-flush">
          {signals.length > 0 ? (
            signals.map((signal) => (
              <div key={signal.id} className="list-group-item list-group-item-action">
                <div className="d-flex w-100 justify-content-between">
                  <h5 className={`mb-1 ${signal.type === 'buy' ? 'text-success' : 'text-danger'}`}>
                    {signal.type === 'buy' ? 'شراء / Buy' : 'بيع / Sell'}
                  </h5>
                  <small>{signal.timestamp}</small>
                </div>
                <p className="mb-1">
                  <strong>السبب / Reason:</strong> {signal.reason}
                </p>
                <p className="mb-1">
                  <strong>المؤشر / Indicator:</strong> {signal.indicator}
                </p>
                <p className="mb-0">
                  <strong>السعر / Price:</strong> ${signal.price.toFixed(2)}
                </p>
              </div>
            ))
          ) : (
            <div className="list-group-item text-center">
              لا توجد إشارات حالياً / No signals available
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SignalsList;
