import React, { useState } from 'react';

interface LanguageSelectorProps {
  currentLanguage: string;
  onLanguageChange: (language: string) => void;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ currentLanguage, onLanguageChange }) => {
  return (
    <div className="language-selector">
      <div className="btn-group">
        <button 
          className={`btn ${currentLanguage === 'ar' ? 'btn-primary' : 'btn-outline-primary'}`}
          onClick={() => onLanguageChange('ar')}
        >
          العربية
        </button>
        <button 
          className={`btn ${currentLanguage === 'en' ? 'btn-primary' : 'btn-outline-primary'}`}
          onClick={() => onLanguageChange('en')}
        >
          English
        </button>
      </div>
    </div>
  );
};

export default LanguageSelector;
