// Technical Analysis Service for Crypto Analysis App

import { CandleData } from './api';

// Types for technical indicators
export interface RSIData {
  time: string;
  value: number;
}

export interface MACDData {
  time: string;
  macd: number;
  signal: number;
  histogram: number;
}

export interface OBVData {
  time: string;
  value: number;
}

export interface MAData {
  time: string;
  ma9: number;
  ma20: number;
}

export interface Signal {
  id: string;
  type: 'buy' | 'sell';
  reason: string;
  indicator: string;
  timestamp: string;
  price: number;
}

/**
 * Calculate Relative Strength Index (RSI)
 * @param data - Array of candle data
 * @param period - RSI period (default: 14)
 */
export const calculateRSI = (data: CandleData[], period: number = 14): RSIData[] => {
  if (data.length < period + 1) {
    return [];
  }

  const rsiData: RSIData[] = [];
  const prices = data.map(candle => candle.close);
  
  let gains = 0;
  let losses = 0;

  // Calculate initial average gain and loss
  for (let i = 1; i <= period; i++) {
    const change = prices[i] - prices[i - 1];
    if (change >= 0) {
      gains += change;
    } else {
      losses += Math.abs(change);
    }
  }

  let avgGain = gains / period;
  let avgLoss = losses / period;

  // Calculate RSI for each data point
  for (let i = period + 1; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1];
    let currentGain = 0;
    let currentLoss = 0;

    if (change >= 0) {
      currentGain = change;
    } else {
      currentLoss = Math.abs(change);
    }

    // Use smoothed averages
    avgGain = ((avgGain * (period - 1)) + currentGain) / period;
    avgLoss = ((avgLoss * (period - 1)) + currentLoss) / period;

    const rs = avgGain / (avgLoss === 0 ? 0.001 : avgLoss); // Avoid division by zero
    const rsi = 100 - (100 / (1 + rs));

    rsiData.push({
      time: data[i].time,
      value: rsi
    });
  }

  return rsiData;
};

/**
 * Calculate Moving Average Convergence Divergence (MACD)
 * @param data - Array of candle data
 * @param fastPeriod - Fast EMA period (default: 12)
 * @param slowPeriod - Slow EMA period (default: 26)
 * @param signalPeriod - Signal EMA period (default: 9)
 */
export const calculateMACD = (
  data: CandleData[],
  fastPeriod: number = 12,
  slowPeriod: number = 26,
  signalPeriod: number = 9
): MACDData[] => {
  if (data.length < slowPeriod + signalPeriod) {
    return [];
  }

  const macdData: MACDData[] = [];
  const prices = data.map(candle => candle.close);
  
  // Calculate EMAs
  const fastEMA = calculateEMA(prices, fastPeriod);
  const slowEMA = calculateEMA(prices, slowPeriod);
  
  // Calculate MACD line
  const macdLine: number[] = [];
  for (let i = 0; i < prices.length; i++) {
    if (i < slowPeriod - 1) {
      macdLine.push(0);
    } else {
      macdLine.push(fastEMA[i] - slowEMA[i]);
    }
  }
  
  // Calculate signal line (EMA of MACD line)
  const signalLine = calculateEMA(macdLine, signalPeriod);
  
  // Calculate histogram
  for (let i = slowPeriod + signalPeriod - 2; i < prices.length; i++) {
    macdData.push({
      time: data[i].time,
      macd: macdLine[i],
      signal: signalLine[i],
      histogram: macdLine[i] - signalLine[i]
    });
  }
  
  return macdData;
};

/**
 * Calculate On-Balance Volume (OBV)
 * @param data - Array of candle data with volume
 */
export const calculateOBV = (data: CandleData[]): OBVData[] => {
  if (data.length < 2 || !data[0].volume) {
    return [];
  }

  const obvData: OBVData[] = [];
  let obv = 0;
  
  // First point
  obvData.push({
    time: data[0].time,
    value: obv
  });
  
  // Calculate OBV for each data point
  for (let i = 1; i < data.length; i++) {
    const currentClose = data[i].close;
    const previousClose = data[i - 1].close;
    const currentVolume = data[i].volume || 0;
    
    if (currentClose > previousClose) {
      obv += currentVolume;
    } else if (currentClose < previousClose) {
      obv -= currentVolume;
    }
    // If prices are equal, OBV remains unchanged
    
    obvData.push({
      time: data[i].time,
      value: obv
    });
  }
  
  return obvData;
};

/**
 * Calculate Moving Averages
 * @param data - Array of candle data
 * @param shortPeriod - Short MA period (default: 9)
 * @param longPeriod - Long MA period (default: 20)
 * @param type - MA type ('sma' or 'ema', default: 'ema')
 */
export const calculateMovingAverages = (
  data: CandleData[],
  shortPeriod: number = 9,
  longPeriod: number = 20,
  type: 'sma' | 'ema' = 'ema'
): MAData[] => {
  if (data.length < longPeriod) {
    return [];
  }

  const maData: MAData[] = [];
  const prices = data.map(candle => candle.close);
  
  let shortMA: number[];
  let longMA: number[];
  
  if (type === 'ema') {
    shortMA = calculateEMA(prices, shortPeriod);
    longMA = calculateEMA(prices, longPeriod);
  } else {
    shortMA = calculateSMA(prices, shortPeriod);
    longMA = calculateSMA(prices, longPeriod);
  }
  
  for (let i = longPeriod - 1; i < prices.length; i++) {
    maData.push({
      time: data[i].time,
      ma9: shortMA[i],
      ma20: longMA[i]
    });
  }
  
  return maData;
};

/**
 * Detect RSI Divergence
 * @param candles - Array of candle data
 * @param rsiData - Array of RSI data
 */
export const detectRSIDivergence = (candles: CandleData[], rsiData: RSIData[]): Signal[] => {
  const signals: Signal[] = [];
  
  // Need at least 30 data points for reliable divergence detection
  if (candles.length < 30 || rsiData.length < 30) {
    return signals;
  }
  
  // Find local price highs and lows
  const priceHighs: {index: number, price: number}[] = [];
  const priceLows: {index: number, price: number}[] = [];
  
  // Find local RSI highs and lows
  const rsiHighs: {index: number, value: number}[] = [];
  const rsiLows: {index: number, value: number}[] = [];
  
  // Simple peak detection (in a real app, use more sophisticated algorithms)
  for (let i = 5; i < candles.length - 5; i++) {
    // Price highs
    if (candles[i].high > candles[i-1].high && 
        candles[i].high > candles[i-2].high &&
        candles[i].high > candles[i+1].high && 
        candles[i].high > candles[i+2].high) {
      priceHighs.push({index: i, price: candles[i].high});
    }
    
    // Price lows
    if (candles[i].low < candles[i-1].low && 
        candles[i].low < candles[i-2].low &&
        candles[i].low < candles[i+1].low && 
        candles[i].low < candles[i+2].low) {
      priceLows.push({index: i, price: candles[i].low});
    }
    
    // RSI highs
    const rsiIndex = i - 14; // Adjust for RSI offset
    if (rsiIndex >= 0 && rsiIndex < rsiData.length) {
      if (rsiData[rsiIndex].value > 70 &&
          rsiData[rsiIndex].value > (rsiIndex > 0 ? rsiData[rsiIndex-1].value : 0) &&
          rsiData[rsiIndex].value > (rsiIndex > 1 ? rsiData[rsiIndex-2].value : 0) &&
          rsiData[rsiIndex].value > (rsiIndex < rsiData.length - 1 ? rsiData[rsiIndex+1].value : 100) &&
          rsiData[rsiIndex].value > (rsiIndex < rsiData.length - 2 ? rsiData[rsiIndex+2].value : 100)) {
        rsiHighs.push({index: i, value: rsiData[rsiIndex].value});
      }
      
      // RSI lows
      if (rsiData[rsiIndex].value < 30 &&
          rsiData[rsiIndex].value < (rsiIndex > 0 ? rsiData[rsiIndex-1].value : 100) &&
          rsiData[rsiIndex].value < (rsiIndex > 1 ? rsiData[rsiIndex-2].value : 100) &&
          rsiData[rsiIndex].value < (rsiIndex < rsiData.length - 1 ? rsiData[rsiIndex+1].value : 0) &&
          rsiData[rsiIndex].value < (rsiIndex < rsiData.length - 2 ? rsiData[rsiIndex+2].value : 0)) {
        rsiLows.push({index: i, value: rsiData[rsiIndex].value});
      }
    }
  }
  
  // Detect bearish divergence (price makes higher high, RSI makes lower high)
  for (let i = 0; i < priceHighs.length - 1; i++) {
    for (let j = i + 1; j < priceHighs.length; j++) {
      if (priceHighs[j].price > priceHighs[i].price) {
        // Find corresponding RSI highs
        const rsiHigh1 = rsiHighs.find(h => Math.abs(h.index - priceHighs[i].index) < 3);
        const rsiHigh2 = rsiHighs.find(h => Math.abs(h.index - priceHighs[j].index) < 3);
        
        if (rsiHigh1 && rsiHigh2 && rsiHigh2.value < rsiHigh1.value) {
          signals.push({
            id: `rsi-bearish-${priceHighs[j].index}`,
            type: 'sell',
            reason: 'RSI Divergence - قمم مرتفعة في السعر مع قمم منخفضة في RSI',
            indicator: 'RSI Divergence',
            timestamp: candles[priceHighs[j].index].time,
            price: priceHighs[j].price
          });
          break; // Only one signal per pattern
        }
      }
    }
  }
  
  // Detect bullish divergence (price makes lower low, RSI makes higher low)
  for (let i = 0; i < priceLows.length - 1; i++) {
    for (let j = i + 1; j < priceLows.length; j++) {
      if (priceLows[j].price < priceLows[i].price) {
        // Find corresponding RSI lows
        const rsiLow1 = rsiLows.find(l => Math.abs(l.index - priceLows[i].index) < 3);
        const rsiLow2 = rsiLows.find(l => Math.abs(l.index - priceLows[j].index) < 3);
        
        if (rsiLow1 && rsiLow2 && rsiLow2.value > rsiLow1.value) {
          signals.push({
            id: `rsi-bullish-${priceLows[j].index}`,
            type: 'buy',
            reason: 'RSI Divergence - قيعان منخفضة في السعر مع قيعان مرتفعة في RSI',
            indicator: 'RSI Divergence',
            timestamp: candles[priceLows[j].index].time,
            price: priceLows[j].price
          });
          break; // Only one signal per pattern
        }
      }
    }
  }
  
  return signals;
};

/**
 * Detect Moving Average Crossovers
 * @param maData - Array of moving average data
 * @param candles - Array of candle data
 */
export const detectMACrossover = (maData: MAData[], candles: CandleData[]): Signal[] => {
  const signals: Signal[] = [];
  
  if (maData.length < 2) {
    return signals;
  }
  
  for (let i = 1; i < maData.length; i++) {
    // Bullish crossover (short MA crosses above long MA)
    if (maData[i-1].ma9 <= maData[i-1].ma20 && maData[i].ma9 > maData[i].ma20) {
      // Find corresponding candle
      const candle = candles.find(c => c.time === maData[i].time);
      if (candle) {
        signals.push({
          id: `ma-bullish-${i}`,
          type: 'buy',
          reason: 'Moving Average Crossover - تقاطع المتوسط المتحرك 9 فوق المتوسط المتحرك 20',
          indicator: 'Moving Average Crossover',
          timestamp: maData[i].time,
          price: candle.close
        });
      }
    }
    
    // Bearish crossover (short MA crosses below long MA)
    if (maData[i-1].ma9 >= maData[i-1].ma20 && maData[i].ma9 < maData[i].ma20) {
      // Find corresponding candle
      const candle = candles.find(c => c.time === maData[i].time);
      if (candle) {
        signals.push({
          id: `ma-bearish-${i}`,
          type: 'sell',
          reason: 'Moving Average Crossover - تقاطع المتوسط المتحرك 9 تحت المتوسط المتحرك 20',
          indicator: 'Moving Average Crossover',
          timestamp: maData[i].time,
          price: candle.close
        });
      }
    }
  }
  
  return signals;
};

// Helper functions

/**
 * Calculate Simple Moving Average (SMA)
 * @param data - Array of price data
 * @param period - SMA period
 */
const calculateSMA = (data: number[], period: number): number[] => {
  const sma: number[] = [];
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      sma.push(0);
    } else {
      let sum = 0;
      for (let j = 0; j < period; j++) {
        sum += data[i - j];
      }
      sma.push(sum / period);
    }
  }
  
  return sma;
};

/**
 * Calculate Exponential Moving Average (EMA)
 * @param data - Array of price data
 * @param period - EMA period
 */
const calculateEMA = (data: number[], period: number): number[] => {
  const ema: number[] = [];
  const multiplier = 2 / (period + 1);
  
  // Start with SMA for the first EMA value
  let sum = 0;
  for (let i = 0; i < period; i++) {
    sum += data[i];
  }
  
  ema.push(sum / period);
  
  // Calculate EMA for remaining data points
  for (let i = period; i < data.length; i++) {
    ema.push((data[i] - ema[i - period]) * multiplier + ema[i - period]);
  }
  
  // Pad the beginning with zeros to match input length
  const padding = Array(period - 1).fill(0);
  return [...padding, ...ema];
};
