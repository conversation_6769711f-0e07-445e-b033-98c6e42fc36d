# وثيقة متطلبات تطبيق تحليل العملات الرقمية

## المقدمة
هذه الوثيقة تفصل متطلبات تطبيق تحليل العملات الرقمية (Crypto Technical Analysis App) الذي سيتم تطويره باستخدام HTML, CSS, و JavaScript. الهدف الرئيسي للتطبيق هو تقديم إشارات بيع وشراء ذكية بناءً على مجموعة من التحليلات الفنية.

## المؤشرات الفنية المطلوبة

### 1. RSI Divergence
- **الوصف**: اكتشاف الدايفرجنس (التباعد) الإيجابي والسلبي بين السعر ومؤشر RSI.
- **المتطلبات التفصيلية**:
  - حساب مؤشر RSI (Relative Strength Index) بفترة افتراضية 14.
  - تحديد القمم والقيعان في كل من السعر ومؤشر RSI.
  - مقارنة اتجاهات القمم والقيعان بين السعر والمؤشر لاكتشاف التباعد.
  - تحديد الدايفرجنس الإيجابي (قيعان منخفضة في السعر مع قيعان مرتفعة في RSI) كإشارة شراء محتملة.
  - تحديد الدايفرجنس السلبي (قمم مرتفعة في السعر مع قمم منخفضة في RSI) كإشارة بيع محتملة.

### 2. OBV Divergence
- **الوصف**: مقارنة حركة السعر مع On-Balance Volume وتحليل الاختلافات.
- **المتطلبات التفصيلية**:
  - حساب مؤشر OBV (On-Balance Volume) بناءً على بيانات الحجم والسعر.
  - تحديد القمم والقيعان في كل من السعر ومؤشر OBV.
  - مقارنة اتجاهات القمم والقيعان بين السعر والمؤشر لاكتشاف التباعد.
  - تحديد الدايفرجنس الإيجابي (قيعان منخفضة في السعر مع قيعان مرتفعة في OBV) كإشارة شراء محتملة.
  - تحديد الدايفرجنس السلبي (قمم مرتفعة في السعر مع قمم منخفضة في OBV) كإشارة بيع محتملة.

### 3. MACD Divergence
- **الوصف**: التعرف على الدايفرجنس بين السعر و MACD histogram.
- **المتطلبات التفصيلية**:
  - حساب مؤشر MACD (Moving Average Convergence Divergence) بالإعدادات الافتراضية (12, 26, 9).
  - تحديد القمم والقيعان في كل من السعر ومؤشر MACD histogram.
  - مقارنة اتجاهات القمم والقيعان بين السعر والمؤشر لاكتشاف التباعد.
  - تحديد الدايفرجنس الإيجابي كإشارة شراء محتملة.
  - تحديد الدايفرجنس السلبي كإشارة بيع محتملة.

### 4. Moving Average Crossover
- **الوصف**: إصدار إشارات عند تقاطع المتوسطات المتحركة 9 و 20 (EMA أو SMA).
- **المتطلبات التفصيلية**:
  - حساب المتوسطات المتحركة EMA و SMA لفترات 9 و 20.
  - توفير خيار للمستخدم للاختيار بين EMA و SMA.
  - اكتشاف تقاطع المتوسط المتحرك 9 فوق المتوسط المتحرك 20 كإشارة شراء.
  - اكتشاف تقاطع المتوسط المتحرك 9 تحت المتوسط المتحرك 20 كإشارة بيع.
  - عرض المتوسطات المتحركة على الرسم البياني بألوان مختلفة.

## الأطر الزمنية المطلوبة

- **4 ساعات**: تحليل البيانات على فترات زمنية مدتها 4 ساعات.
- **3 ساعات**: تحليل البيانات على فترات زمنية مدتها 3 ساعات.
- **ساعتين**: تحليل البيانات على فترات زمنية مدتها ساعتين.
- **يومي**: تحليل البيانات على فترات زمنية يومية.
- **أسبوعي**: تحليل البيانات على فترات زمنية أسبوعية.

**متطلبات إضافية للأطر الزمنية**:
- إمكانية التبديل بين الأطر الزمنية المختلفة بسهولة.
- حفظ الإطار الزمني المفضل للمستخدم.
- إعادة حساب المؤشرات الفنية تلقائياً عند تغيير الإطار الزمني.

## واجهة المستخدم

### 1. واجهة رسومية للشموع اليابانية
- **الوصف**: عرض الرسم البياني للشموع اليابانية مع إمكانية اختيار الإطار الزمني.
- **المتطلبات التفصيلية**:
  - رسم بياني تفاعلي للشموع اليابانية.
  - إمكانية التكبير والتصغير والتمرير في الرسم البياني.
  - عرض معلومات السعر والحجم عند تمرير المؤشر فوق الشموع.
  - إمكانية تخصيص ألوان الشموع (الصعود والهبوط).
  - دعم عرض المؤشرات الفنية على نفس الرسم البياني أو في رسوم بيانية منفصلة.

### 2. زر مخصص لتغيير الإطار الزمني
- **الوصف**: زر لتبديل الإطار الزمني (Toggle Timeframe Button).
- **المتطلبات التفصيلية**:
  - قائمة منسدلة أو مجموعة أزرار لاختيار الإطار الزمني.
  - تمييز الإطار الزمني النشط حالياً.
  - تحديث الرسم البياني والمؤشرات الفنية تلقائياً عند تغيير الإطار الزمني.

### 3. عرض إشارات الشراء والبيع
- **الوصف**: عرض إشارات الشراء والبيع على الرسم البياني مباشرة مع توضيح سبب الإشارة.
- **المتطلبات التفصيلية**:
  - تمييز إشارات الشراء والبيع بألوان وأشكال مختلفة على الرسم البياني.
  - عرض معلومات تفصيلية عن سبب الإشارة عند النقر عليها.
  - إمكانية تصفية الإشارات حسب نوع المؤشر الفني.
  - عرض قائمة بالإشارات الأخيرة مع تفاصيلها.
  - إمكانية تفعيل/تعطيل عرض الإشارات لكل مؤشر فني.

### 4. واجهة متعددة اللغات
- **الوصف**: واجهة باللغة العربية والإنجليزية (تعدد اللغات).
- **المتطلبات التفصيلية**:
  - زر لتبديل اللغة بين العربية والإنجليزية.
  - ترجمة جميع عناصر واجهة المستخدم.
  - دعم الاتجاه من اليمين إلى اليسار للغة العربية.
  - حفظ تفضيل اللغة للمستخدم.

## التقنيات المطلوبة

### 1. واجهات برمجة التطبيقات لبيانات السوق
- **الوصف**: استخدام API من TradingView أو Binance أو CoinGecko للحصول على بيانات السوق.
- **المتطلبات التفصيلية**:
  - دعم الاتصال بواجهة برمجة تطبيقات واحدة أو أكثر من المذكورة.
  - جلب بيانات الشموع اليابانية التاريخية.
  - جلب بيانات الأسعار في الوقت الحقيقي (إن أمكن).
  - التعامل مع حدود معدل الطلبات (Rate Limits).
  - تخزين البيانات مؤقتاً لتقليل عدد الطلبات.

### 2. تحليل البيانات
- **الوصف**: تحليل البيانات محلياً أو باستخدام سيرفر خارجي (Node.js, Python backend).
- **المتطلبات التفصيلية**:
  - تنفيذ خوارزميات التحليل الفني في الواجهة الأمامية أو الخلفية.
  - معالجة البيانات التاريخية لحساب المؤشرات الفنية.
  - تحسين أداء الحسابات للتعامل مع كميات كبيرة من البيانات.
  - تخزين نتائج التحليل مؤقتاً لتحسين الأداء.

### 3. تخزين البيانات والمصادقة
- **الوصف**: Firebase أو Supabase لتسجيل الدخول وتخزين إعدادات المستخدم.
- **المتطلبات التفصيلية**:
  - تسجيل المستخدمين وإدارة الحسابات.
  - تخزين إعدادات المستخدم (الإطار الزمني المفضل، اللغة، إلخ).
  - تخزين الإشارات المخصصة أو المفضلة للمستخدم.
  - إدارة الصلاحيات والأمان.

### 4. الإشعارات
- **الوصف**: إرسال إشعارات لحظية عندما تحدث إشارة جديدة (Push Notifications).
- **المتطلبات التفصيلية**:
  - دعم إشعارات الويب (Web Push Notifications).
  - إمكانية تخصيص الإشعارات حسب نوع الإشارة والعملة.
  - إدارة اشتراكات الإشعارات.
  - عرض سجل الإشعارات السابقة.

## الذكاء الاصطناعي (اختياري)

### 1. دعم التوصيات
- **الوصف**: دعم التوصيات بناءً على تحليل البيانات السابقة.
- **المتطلبات التفصيلية**:
  - تحليل أداء الإشارات السابقة.
  - تقييم دقة كل مؤشر فني في ظروف السوق المختلفة.
  - تقديم توصيات مخصصة بناءً على نمط السوق الحالي.
  - عرض إحصائيات نجاح الإشارات.

### 2. تحليل معنوي للسوق
- **الوصف**: تحليل معنوي للسوق (Sentiment Analysis من أخبار أو Twitter APIs).
- **المتطلبات التفصيلية**:
  - جمع البيانات من مصادر الأخبار و/أو وسائل التواصل الاجتماعي.
  - تحليل المشاعر (إيجابية، سلبية، محايدة) تجاه العملات الرقمية.
  - دمج نتائج التحليل المعنوي مع التحليل الفني.
  - عرض مؤشر المشاعر العام للسوق.

## متطلبات غير وظيفية

### 1. الأداء
- تحميل سريع للرسوم البيانية والبيانات.
- استجابة سريعة عند تغيير الإطار الزمني أو العملة.
- تحسين أداء حسابات المؤشرات الفنية.

### 2. قابلية الاستخدام
- واجهة مستخدم بديهية وسهلة الاستخدام.
- توافق مع مختلف أحجام الشاشات (تصميم متجاوب).
- دعم الأجهزة المحمولة والحواسيب المكتبية.

### 3. الأمان
- حماية بيانات المستخدم.
- اتصال آمن مع واجهات برمجة التطبيقات.
- التحقق من صحة المدخلات.

### 4. قابلية التوسع
- إمكانية إضافة عملات رقمية جديدة.
- إمكانية إضافة مؤشرات فنية جديدة.
- إمكانية إضافة لغات جديدة.
