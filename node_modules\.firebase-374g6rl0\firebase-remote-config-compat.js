!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)}(this,function(qe,Ke){"use strict";try{!(function(){function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var u,t,a,r=e(qe);function g(){try{return"object"==typeof indexedDB}catch(e){return}}class c extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,c.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,i.prototype.create)}}class i{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var a,r=t[0]||{},i=`${this.service}/${e}`,s=this.errors[e],s=s?(a=r,s.replace(n,(e,t)=>{var r=a[t];return null!=r?String(r):`<${t}?>`})):"Error",s=`${this.serviceName}: ${s} (${i}).`;return new c(i,s,r)}}const n=/\{\$([^}]+)}/g;function s(e){return e&&e._delegate?e._delegate:e}class o{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}(t=u=u||{})[t.DEBUG=0]="DEBUG",t[t.VERBOSE=1]="VERBOSE",t[t.INFO=2]="INFO",t[t.WARN=3]="WARN",t[t.ERROR=4]="ERROR",t[t.SILENT=5]="SILENT";const l={debug:u.DEBUG,verbose:u.VERBOSE,info:u.INFO,warn:u.WARN,error:u.ERROR,silent:u.SILENT},h=u.INFO,f={[u.DEBUG]:"log",[u.VERBOSE]:"log",[u.INFO]:"info",[u.WARN]:"warn",[u.ERROR]:"error"},d=(e,t,...r)=>{if(!(t<e.logLevel)){var a=(new Date).toISOString(),i=f[t];if(!i)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[i](`[${a}]  ${e.name}:`,...r)}};class p{constructor(e){this.name=e,this._logLevel=h,this._logHandler=d,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in u))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?l[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,u.DEBUG,...e),this._logHandler(this,u.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,u.VERBOSE,...e),this._logHandler(this,u.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,u.INFO,...e),this._logHandler(this,u.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,u.WARN,...e),this._logHandler(this,u.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,u.ERROR,...e),this._logHandler(this,u.ERROR,...e)}}const m=(t,e)=>e.some(e=>t instanceof e);let v,w;const _=new WeakMap,y=new WeakMap,b=new WeakMap,S=new WeakMap,E=new WeakMap;let C={get(e,t,r){if(e instanceof IDBTransaction){if("done"===t)return y.get(e);if("objectStoreNames"===t)return e.objectStoreNames||b.get(e);if("store"===t)return r.objectStoreNames[1]?void 0:r.objectStore(r.objectStoreNames[0])}return L(e[t])},set(e,t,r){return e[t]=r,!0},has(e,t){return e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e}};function I(a){return a!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(w=w||[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey]).includes(a)?function(...e){return a.apply(M(this),e),L(_.get(this))}:function(...e){return L(a.apply(M(this),e))}:function(e,...t){var r=a.call(M(this),e,...t);return b.set(r,e.sort?e.sort():[e]),L(r)}}function T(e){return"function"==typeof e?I(e):(e instanceof IDBTransaction&&(s=e,y.has(s)||(t=new Promise((e,t)=>{const r=()=>{s.removeEventListener("complete",a),s.removeEventListener("error",i),s.removeEventListener("abort",i)},a=()=>{e(),r()},i=()=>{t(s.error||new DOMException("AbortError","AbortError")),r()};s.addEventListener("complete",a),s.addEventListener("error",i),s.addEventListener("abort",i)}),y.set(s,t))),m(e,v=v||[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])?new Proxy(e,C):e);var s,t}function L(e){if(e instanceof IDBRequest)return function(s){const e=new Promise((e,t)=>{const r=()=>{s.removeEventListener("success",a),s.removeEventListener("error",i)},a=()=>{e(L(s.result)),r()},i=()=>{t(s.error),r()};s.addEventListener("success",a),s.addEventListener("error",i)});return e.then(e=>{e instanceof IDBCursor&&_.set(e,s)}).catch(()=>{}),E.set(e,s),e}(e);if(S.has(e))return S.get(e);var t=T(e);return t!==e&&(S.set(e,t),E.set(t,e)),t}const M=e=>E.get(e);const D=["get","getKey","getAll","getAllKeys","count"],F=["put","add","delete","clear"],P=new Map;function k(e,t){if(e instanceof IDBDatabase&&!(t in e)&&"string"==typeof t){if(P.get(t))return P.get(t);const i=t.replace(/FromIndex$/,""),s=t!==i,n=F.includes(i);if(i in(s?IDBIndex:IDBObjectStore).prototype&&(n||D.includes(i))){var r=async function(e,...t){var r=this.transaction(e,n?"readwrite":"readonly");let a=r.store;return s&&(a=a.index(t.shift())),(await Promise.all([a[i](...t),n&&r.done]))[0]};return P.set(t,r),r}}}C={...a=C,get:(e,t,r)=>k(e,t)||a.get(e,t,r),has:(e,t)=>!!k(e,t)||a.has(e,t)};var O="@firebase/installations",N="0.6.9";const j=1e4,A=`w:${N}`,R="FIS_v2",B="https://firebaseinstallations.googleapis.com/v1",$=36e5;var H;const x=new i("installations","Installations",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."});function V(e){return e instanceof c&&e.code.includes("request-failed")}function q({projectId:e}){return`${B}/projects/${e}/installations`}function K(e){return{token:e.token,requestStatus:2,expiresIn:(e=e.expiresIn,Number(e.replace("s","000"))),creationTime:Date.now()}}async function U(e,t){var r=(await t.json()).error;return x.create("request-failed",{requestName:e,serverCode:r.code,serverMessage:r.message,serverStatus:r.status})}function z({apiKey:e}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e})}function G(e,{refreshToken:t}){const r=z(e);return r.append("Authorization",(t=t,`${R} ${t}`)),r}async function W(e){var t=await e();return 500<=t.status&&t.status<600?e():t}function J(t){return new Promise(e=>{setTimeout(e,t)})}const Y=/^[cdef][\w-]{21}$/,Z="";function Q(){try{const t=new Uint8Array(17),r=self.crypto||self.msCrypto;r.getRandomValues(t),t[0]=112+t[0]%16;var e=function(e){const t=function(e){const t=btoa(String.fromCharCode(...e));return t.replace(/\+/g,"-").replace(/\//g,"_")}(e);return t.substr(0,22)}(t);return Y.test(e)?e:Z}catch(e){return Z}}function X(e){return`${e.appName}!${e.appId}`}const ee=new Map;function te(e,t){var r=X(e);re(r,t),function(e,t){const r=function(){!ae&&"BroadcastChannel"in self&&(ae=new BroadcastChannel("[Firebase] FID Change"),ae.onmessage=e=>{re(e.data.key,e.data.fid)});return ae}();r&&r.postMessage({key:e,fid:t});0===ee.size&&ae&&(ae.close(),ae=null)}(r,t)}function re(e,t){var r=ee.get(e);if(r)for(const a of r)a(t)}let ae=null;const ie="firebase-installations-store";let se=null;function ne(){return se=se||function(e,t,{blocked:r,upgrade:a,blocking:i,terminated:s}){const n=indexedDB.open(e,t),o=L(n);return a&&n.addEventListener("upgradeneeded",e=>{a(L(n.result),e.oldVersion,e.newVersion,L(n.transaction),e)}),r&&n.addEventListener("blocked",e=>r(e.oldVersion,e.newVersion,e)),o.then(e=>{s&&e.addEventListener("close",()=>s()),i&&e.addEventListener("versionchange",e=>i(e.oldVersion,e.newVersion,e))}).catch(()=>{}),o}("firebase-installations-database",1,{upgrade:(e,t)=>{0===t&&e.createObjectStore(ie)}}),se}async function oe(e,t){var r=X(e);const a=await ne(),i=a.transaction(ie,"readwrite"),s=i.objectStore(ie);var n=await s.get(r);return await s.put(t,r),await i.done,n&&n.fid===t.fid||te(e,t.fid),t}async function ce(e){var t=X(e);const r=await ne(),a=r.transaction(ie,"readwrite");await a.objectStore(ie).delete(t),await a.done}async function le(e,t){var r=X(e);const a=await ne(),i=a.transaction(ie,"readwrite"),s=i.objectStore(ie);var n=await s.get(r),o=t(n);return void 0===o?await s.delete(r):await s.put(o,r),await i.done,!o||n&&n.fid===o.fid||te(e,o.fid),o}async function ue(r){let a;var e=await le(r.appConfig,e=>{var t=he(e||{fid:Q(),registrationStatus:0}),t=function(e,t){{if(0!==t.registrationStatus)return 1===t.registrationStatus?{installationEntry:t,registrationPromise:async function(e){let t=await ge(e.appConfig);for(;1===t.registrationStatus;)await J(100),t=await ge(e.appConfig);if(0!==t.registrationStatus)return t;{var{installationEntry:r,registrationPromise:a}=await ue(e);return a||r}}(e)}:{installationEntry:t};if(!navigator.onLine){var r=Promise.reject(x.create("app-offline"));return{installationEntry:t,registrationPromise:r}}var a={fid:t.fid,registrationStatus:1,registrationTime:Date.now()},r=async function(t,r){try{var e=await async function({appConfig:e,heartbeatServiceProvider:t},{fid:r}){const a=q(e),i=z(e),s=t.getImmediate({optional:!0});!s||(n=await s.getHeartbeatsHeader())&&i.append("x-firebase-client",n);var n={fid:r,authVersion:R,appId:e.appId,sdkVersion:A};const o={method:"POST",headers:i,body:JSON.stringify(n)},c=await W(()=>fetch(a,o));if(c.ok){n=await c.json();return{fid:n.fid||r,registrationStatus:2,refreshToken:n.refreshToken,authToken:K(n.authToken)}}throw await U("Create Installation",c)}(t,r);return oe(t.appConfig,e)}catch(e){throw V(e)&&409===e.customData.serverCode?await ce(t.appConfig):await oe(t.appConfig,{fid:r.fid,registrationStatus:0}),e}}(e,a);return{installationEntry:a,registrationPromise:r}}}(r,t);return a=t.registrationPromise,t.installationEntry});return e.fid===Z?{installationEntry:await a}:{installationEntry:e,registrationPromise:a}}function ge(e){return le(e,e=>{if(!e)throw x.create("installation-not-found");return he(e)})}function he(e){return 1===(t=e).registrationStatus&&t.registrationTime+j<Date.now()?{fid:e.fid,registrationStatus:0}:e;var t}async function fe({appConfig:e,heartbeatServiceProvider:t},r){const a=([i,s]=[e,r["fid"]],`${q(i)}/${s}/authTokens:generate`);var i,s;const n=G(e,r),o=t.getImmediate({optional:!0});!o||(c=await o.getHeartbeatsHeader())&&n.append("x-firebase-client",c);var c={installation:{sdkVersion:A,appId:e.appId}};const l={method:"POST",headers:n,body:JSON.stringify(c)},u=await W(()=>fetch(a,l));if(u.ok)return K(await u.json());throw await U("Generate Auth Token",u)}async function de(a,i=!1){let s;var e=await le(a.appConfig,e=>{if(!me(e))throw x.create("not-registered");var t,r=e.authToken;if(i||2!==(t=r).requestStatus||function(e){var t=Date.now();return t<e.creationTime||e.creationTime+e.expiresIn<t+$}(t)){if(1===r.requestStatus)return s=async function(e,t){let r=await pe(e.appConfig);for(;1===r.authToken.requestStatus;)await J(100),r=await pe(e.appConfig);var a=r.authToken;return 0===a.requestStatus?de(e,t):a}(a,i),e;if(!navigator.onLine)throw x.create("app-offline");r=(t=e,r={requestStatus:1,requestTime:Date.now()},Object.assign(Object.assign({},t),{authToken:r}));return s=async function(t,r){try{var a=await fe(t,r),e=Object.assign(Object.assign({},r),{authToken:a});return await oe(t.appConfig,e),a}catch(e){throw!V(e)||401!==e.customData.serverCode&&404!==e.customData.serverCode?(a=Object.assign(Object.assign({},r),{authToken:{requestStatus:0}}),await oe(t.appConfig,a)):await ce(t.appConfig),e}}(a,r),r}return e});return s?await s:e.authToken}function pe(e){return le(e,e=>{if(!me(e))throw x.create("not-registered");var t,r=e.authToken;return 1===(t=r).requestStatus&&t.requestTime+j<Date.now()?Object.assign(Object.assign({},e),{authToken:{requestStatus:0}}):e})}function me(e){return void 0!==e&&2===e.registrationStatus}async function ve(e,t=!1){var r,a=e;return await((r=(await ue(a)).registrationPromise)&&await r),(await de(a,t)).token}function we(e){return x.create("missing-app-config-values",{valueName:e})}const _e="installations",ye=e=>{var t=e.getProvider("app").getImmediate();return{app:t,appConfig:function(e){if(!e||!e.options)throw we("App Configuration");if(!e.name)throw we("App Name");for(const t of["projectId","apiKey","appId"])if(!e.options[t])throw we(t);return{appName:e.name,projectId:e.options.projectId,apiKey:e.options.apiKey,appId:e.options.appId}}(t),heartbeatServiceProvider:Ke._getProvider(t,"heartbeat"),_delete:()=>Promise.resolve()}},be=e=>{var t=e.getProvider("app").getImmediate();const r=Ke._getProvider(t,_e).getImmediate();return{getId:()=>async function(e){var t=e;const{installationEntry:r,registrationPromise:a}=await ue(t);return(a||de(t)).catch(console.error),r.fid}(r),getToken:e=>ve(r,e)}};Ke._registerComponent(new o(_e,ye,"PUBLIC")),Ke._registerComponent(new o("installations-internal",be,"PRIVATE")),Ke.registerVersion(O,N),Ke.registerVersion(O,N,"esm2017");const Se="@firebase/remote-config";class Ee{constructor(){this.listeners=[]}addEventListener(e){this.listeners.push(e)}abort(){this.listeners.forEach(e=>e())}}const Ce=new i("remoteconfig","Remote Config",{"registration-window":"Undefined window object. This SDK only supports usage in a browser environment.","registration-project-id":"Undefined project identifier. Check Firebase app initialization.","registration-api-key":"Undefined API key. Check Firebase app initialization.","registration-app-id":"Undefined app identifier. Check Firebase app initialization.","storage-open":"Error thrown when opening storage. Original error: {$originalErrorMessage}.","storage-get":"Error thrown when reading from storage. Original error: {$originalErrorMessage}.","storage-set":"Error thrown when writing to storage. Original error: {$originalErrorMessage}.","storage-delete":"Error thrown when deleting from storage. Original error: {$originalErrorMessage}.","fetch-client-network":"Fetch client failed to connect to a network. Check Internet connection. Original error: {$originalErrorMessage}.","fetch-timeout":'The config fetch request timed out.  Configure timeout using "fetchTimeoutMillis" SDK setting.',"fetch-throttle":'The config fetch request timed out while in an exponential backoff state. Configure timeout using "fetchTimeoutMillis" SDK setting. Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',"fetch-client-parse":"Fetch client could not parse response. Original error: {$originalErrorMessage}.","fetch-status":"Fetch server returned an HTTP error status. HTTP status: {$httpStatus}.","indexed-db-unavailable":"Indexed DB is not supported by current browser"});const Ie=["1","true","t","yes","y","on"];class Te{constructor(e,t=""){this._source=e,this._value=t}asString(){return this._value}asBoolean(){return"static"!==this._source&&0<=Ie.indexOf(this._value.toLowerCase())}asNumber(){if("static"===this._source)return 0;let e=Number(this._value);return isNaN(e)&&(e=0),e}getSource(){return this._source}}async function Le(e){const t=s(e);var[r,a]=await Promise.all([t._storage.getLastSuccessfulFetchResponse(),t._storage.getActiveConfigEtag()]);return!!(r&&r.config&&r.eTag&&r.eTag!==a)&&(await Promise.all([t._storageCache.setActiveConfig(r.config),t._storage.setActiveConfigEtag(r.eTag)]),!0)}function Me(e){const t=s(e);return t._initializePromise||(t._initializePromise=t._storageCache.loadFromStorage().then(()=>{t._isInitializationComplete=!0})),t._initializePromise}async function De(t){const r=s(t),e=new Ee;setTimeout(async()=>{e.abort()},r.settings.fetchTimeoutMillis);try{await r._client.fetch({cacheMaxAgeMillis:r.settings.minimumFetchIntervalMillis,signal:e}),await r._storageCache.setLastFetchStatus("success")}catch(e){var a=(i="fetch-throttle",(t=e)instanceof c&&-1!==t.code.indexOf(i)?"throttle":"failure");throw await r._storageCache.setLastFetchStatus(a),e}var i}function Fe(r){const e=s(r);return[t={},a={}]=[e._storageCache.getActiveConfig(),e.defaultConfig],Object.keys(Object.assign(Object.assign({},t),a)).reduce((e,t)=>(e[t]=Pe(r,t),e),{});var t,a}function Pe(e,t){const r=s(e);r._isInitializationComplete||r._logger.debug(`A value was requested for key "${t}" before SDK initialization completed.`+" Await on ensureInitialized if the intent was to get a previously activated value.");var a=r._storageCache.getActiveConfig();return a&&void 0!==a[t]?new Te("remote",a[t]):r.defaultConfig&&void 0!==r.defaultConfig[t]?new Te("default",String(r.defaultConfig[t])):(r._logger.debug(`Returning static value for key "${t}".`+" Define a default or remote value if this is unintentional."),new Te("static"))}class ke{constructor(e,t,r,a){this.client=e,this.storage=t,this.storageCache=r,this.logger=a}isCachedDataFresh(e,t){if(!t)return this.logger.debug("Config fetch cache check. Cache unpopulated."),!1;var r=Date.now()-t,a=r<=e;return this.logger.debug("Config fetch cache check."+` Cache age millis: ${r}.`+` Cache max age millis (minimumFetchIntervalMillis setting): ${e}.`+` Is cache hit: ${a}.`),a}async fetch(e){var[t,r]=await Promise.all([this.storage.getLastSuccessfulFetchTimestampMillis(),this.storage.getLastSuccessfulFetchResponse()]);if(r&&this.isCachedDataFresh(e.cacheMaxAgeMillis,t))return r;e.eTag=r&&r.eTag;r=await this.client.fetch(e);const a=[this.storageCache.setLastSuccessfulFetchTimestampMillis(Date.now())];return 200===r.status&&a.push(this.storage.setLastSuccessfulFetchResponse(r)),await Promise.all(a),r}}class Oe{constructor(e,t,r,a,i,s){this.firebaseInstallations=e,this.sdkVersion=t,this.namespace=r,this.projectId=a,this.apiKey=i,this.appId=s}async fetch(r){var e,[t,a]=await Promise.all([this.firebaseInstallations.getId(),this.firebaseInstallations.getToken()]),i=`${window.FIREBASE_REMOTE_CONFIG_URL_BASE||"https://firebaseremoteconfig.googleapis.com"}/v1/projects/${this.projectId}/namespaces/${this.namespace}:fetch?key=${this.apiKey}`,s={"Content-Type":"application/json","Content-Encoding":"gzip","If-None-Match":r.eTag||"*"},a={sdk_version:this.sdkVersion,app_instance_id:t,app_instance_id_token:a,app_id:this.appId,language_code:(e=navigator).languages&&e.languages[0]||e.language},a={method:"POST",headers:s,body:JSON.stringify(a)},i=fetch(i,a),a=new Promise((e,t)=>{r.signal.addEventListener(()=>{const e=new Error("The operation was aborted.");e.name="AbortError",t(e)})});let n;try{await Promise.race([i,a]),n=await i}catch(e){let t="fetch-client-network";throw"AbortError"===(null==e?void 0:e.name)&&(t="fetch-timeout"),Ce.create(t,{originalErrorMessage:null==e?void 0:e.message})}let o=n.status;i=n.headers.get("ETag")||void 0;let c,l;if(200===n.status){let e;try{e=await n.json()}catch(e){throw Ce.create("fetch-client-parse",{originalErrorMessage:null==e?void 0:e.message})}c=e.entries,l=e.state}if("INSTANCE_STATE_UNSPECIFIED"===l?o=500:"NO_CHANGE"===l?o=304:"NO_TEMPLATE"!==l&&"EMPTY_CONFIG"!==l||(c={}),304!==o&&200!==o)throw Ce.create("fetch-status",{httpStatus:o});return{status:o,eTag:i,config:c}}}class Ne{constructor(e,t){this.client=e,this.storage=t}async fetch(e){var t=await this.storage.getThrottleMetadata()||{backoffCount:0,throttleEndTimeMillis:Date.now()};return this.attemptFetch(e,t)}async attemptFetch(t,{throttleEndTimeMillis:r,backoffCount:a}){var i,s;i=t.signal,s=r,await new Promise((e,t)=>{var r=Math.max(s-Date.now(),0);const a=setTimeout(e,r);i.addEventListener(()=>{clearTimeout(a),t(Ce.create("fetch-throttle",{throttleEndTimeMillis:s}))})});try{var n=await this.client.fetch(t);return await this.storage.deleteThrottleMetadata(),n}catch(e){if(!function(e){if(e instanceof c&&e.customData){var t=Number(e.customData.httpStatus);return 429===t||500===t||503===t||504===t}}(e))throw e;var o={throttleEndTimeMillis:Date.now()+(r=2,n=1e3*Math.pow(r,a),o=Math.round(.5*n*(Math.random()-.5)*2),Math.min(144e5,n+o)),backoffCount:a+1};return await this.storage.setThrottleMetadata(o),this.attemptFetch(t,o)}}}class je{constructor(e,t,r,a,i){this.app=e,this._client=t,this._storageCache=r,this._storage=a,this._logger=i,this._isInitializationComplete=!1,this.settings={fetchTimeoutMillis:6e4,minimumFetchIntervalMillis:432e5},this.defaultConfig={}}get fetchTimeMillis(){return this._storageCache.getLastSuccessfulFetchTimestampMillis()||-1}get lastFetchStatus(){return this._storageCache.getLastFetchStatus()||"no-fetch-yet"}}function Ae(e,t){var r=e.target.error||void 0;return Ce.create(t,{originalErrorMessage:r&&(null==r?void 0:r.message)})}const Re="app_namespace_store";class Be{constructor(e,t,r,a=function(){return new Promise((t,r)=>{try{const e=indexedDB.open("firebase_remote_config",1);e.onerror=e=>{r(Ae(e,"storage-open"))},e.onsuccess=e=>{t(e.target.result)},e.onupgradeneeded=e=>{const t=e.target.result;0===e.oldVersion&&t.createObjectStore(Re,{keyPath:"compositeKey"})}}catch(e){r(Ce.create("storage-open",{originalErrorMessage:null==e?void 0:e.message}))}})}()){this.appId=e,this.appName=t,this.namespace=r,this.openDbPromise=a}getLastFetchStatus(){return this.get("last_fetch_status")}setLastFetchStatus(e){return this.set("last_fetch_status",e)}getLastSuccessfulFetchTimestampMillis(){return this.get("last_successful_fetch_timestamp_millis")}setLastSuccessfulFetchTimestampMillis(e){return this.set("last_successful_fetch_timestamp_millis",e)}getLastSuccessfulFetchResponse(){return this.get("last_successful_fetch_response")}setLastSuccessfulFetchResponse(e){return this.set("last_successful_fetch_response",e)}getActiveConfig(){return this.get("active_config")}setActiveConfig(e){return this.set("active_config",e)}getActiveConfigEtag(){return this.get("active_config_etag")}setActiveConfigEtag(e){return this.set("active_config_etag",e)}getThrottleMetadata(){return this.get("throttle_metadata")}setThrottleMetadata(e){return this.set("throttle_metadata",e)}deleteThrottleMetadata(){return this.delete("throttle_metadata")}async get(n){const o=await this.openDbPromise;return new Promise((r,t)=>{const e=o.transaction([Re],"readonly"),a=e.objectStore(Re);var i=this.createCompositeKey(n);try{const s=a.get(i);s.onerror=e=>{t(Ae(e,"storage-get"))},s.onsuccess=e=>{var t=e.target.result;r(t?t.value:void 0)}}catch(e){t(Ce.create("storage-get",{originalErrorMessage:null==e?void 0:e.message}))}})}async set(n,o){const c=await this.openDbPromise;return new Promise((e,t)=>{const r=c.transaction([Re],"readwrite"),a=r.objectStore(Re);var i=this.createCompositeKey(n);try{const s=a.put({compositeKey:i,value:o});s.onerror=e=>{t(Ae(e,"storage-set"))},s.onsuccess=()=>{e()}}catch(e){t(Ce.create("storage-set",{originalErrorMessage:null==e?void 0:e.message}))}})}async delete(n){const o=await this.openDbPromise;return new Promise((e,t)=>{const r=o.transaction([Re],"readwrite"),a=r.objectStore(Re);var i=this.createCompositeKey(n);try{const s=a.delete(i);s.onerror=e=>{t(Ae(e,"storage-delete"))},s.onsuccess=()=>{e()}}catch(e){t(Ce.create("storage-delete",{originalErrorMessage:null==e?void 0:e.message}))}})}createCompositeKey(e){return[this.appId,this.appName,this.namespace,e].join()}}class $e{constructor(e){this.storage=e}getLastFetchStatus(){return this.lastFetchStatus}getLastSuccessfulFetchTimestampMillis(){return this.lastSuccessfulFetchTimestampMillis}getActiveConfig(){return this.activeConfig}async loadFromStorage(){var e=this.storage.getLastFetchStatus(),t=this.storage.getLastSuccessfulFetchTimestampMillis(),r=this.storage.getActiveConfig(),e=await e;e&&(this.lastFetchStatus=e);t=await t;t&&(this.lastSuccessfulFetchTimestampMillis=t);r=await r;r&&(this.activeConfig=r)}setLastFetchStatus(e){return this.lastFetchStatus=e,this.storage.setLastFetchStatus(e)}setLastSuccessfulFetchTimestampMillis(e){return this.lastSuccessfulFetchTimestampMillis=e,this.storage.setLastSuccessfulFetchTimestampMillis(e)}setActiveConfig(e){return this.activeConfig=e,this.storage.setActiveConfig(e)}}async function He(){if(!g())return!1;try{return await new Promise((t,r)=>{try{let e=!0;const a="validate-browser-context-for-indexeddb-analytics-module",i=self.indexedDB.open(a);i.onsuccess=()=>{i.result.close(),e||self.indexedDB.deleteDatabase(a),t(!0)},i.onupgradeneeded=()=>{e=!1},i.onerror=()=>{var e;r((null===(e=i.error)||void 0===e?void 0:e.message)||"")}}catch(e){r(e)}})}catch(e){return!1}}Ke._registerComponent(new o("remote-config",function(e,{instanceIdentifier:t}){var r=e.getProvider("app").getImmediate(),a=e.getProvider("installations-internal").getImmediate();if("undefined"==typeof window)throw Ce.create("registration-window");if(!g())throw Ce.create("indexed-db-unavailable");var{projectId:i,apiKey:s,appId:n}=r.options;if(!i)throw Ce.create("registration-project-id");if(!s)throw Ce.create("registration-api-key");if(!n)throw Ce.create("registration-app-id");t=t||"firebase";const o=new Be(n,r.name,t),c=new $e(o),l=new p(Se);l.logLevel=u.ERROR;n=new Oe(a,Ke.SDK_VERSION,t,i,s,n),n=new Ne(n,o),n=new ke(n,o,c,l),n=new je(r,n,c,o,l);return Me(n),n},"PUBLIC").setMultipleInstances(!0)),Ke.registerVersion(Se,"0.4.9"),Ke.registerVersion(Se,"0.4.9","esm2017");class xe{constructor(e,t){this.app=e,this._delegate=t}get defaultConfig(){return this._delegate.defaultConfig}set defaultConfig(e){this._delegate.defaultConfig=e}get fetchTimeMillis(){return this._delegate.fetchTimeMillis}get lastFetchStatus(){return this._delegate.lastFetchStatus}get settings(){return this._delegate.settings}set settings(e){this._delegate.settings=e}activate(){return Le(this._delegate)}ensureInitialized(){return Me(this._delegate)}fetch(){return De(this._delegate)}fetchAndActivate(){return async function(e){return await De(e=s(e)),Le(e)}(this._delegate)}getAll(){return Fe(this._delegate)}getBoolean(e){return Pe(s(this._delegate),e).asBoolean()}getNumber(e){return Pe(s(this._delegate),e).asNumber()}getString(e){return Pe(s(this._delegate),e).asString()}getValue(e){return Pe(this._delegate,e)}setLogLevel(e){!function(e,t){const r=s(e);switch(t){case"debug":r._logger.logLevel=u.DEBUG;break;case"silent":r._logger.logLevel=u.SILENT;break;default:r._logger.logLevel=u.ERROR}}(this._delegate,e)}}function Ve(e,{instanceIdentifier:t}){var r=e.getProvider("app-compat").getImmediate(),a=e.getProvider("remote-config").getImmediate({identifier:t});return new xe(r,a)}(H=r.default).INTERNAL.registerComponent(new o("remoteConfig-compat",Ve,"PUBLIC").setMultipleInstances(!0).setServiceProps({isSupported:He})),H.registerVersion("@firebase/remote-config-compat","0.2.9")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-remote-config-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-remote-config-compat.js.map
