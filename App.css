.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* RTL Support */
[dir="rtl"] .card-header {
  text-align: right;
}

[dir="rtl"] .form-check {
  padding-right: 1.5em;
  padding-left: 0;
}

[dir="rtl"] .form-check .form-check-input {
  float: right;
  margin-right: -1.5em;
  margin-left: 0;
}

/* Custom styles for crypto app */
.chart-component {
  position: relative;
}

.signal-marker {
  position: absolute;
  z-index: 100;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.signal-marker.buy {
  background-color: #28a745;
  border: 2px solid #fff;
}

.signal-marker.sell {
  background-color: #dc3545;
  border: 2px solid #fff;
}

.signal-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 200;
  max-width: 200px;
}
