// API Service for Crypto Technical Analysis App

// Types for API responses
export interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}

export interface CryptoSymbol {
  id: string;
  symbol: string;
  name: string;
}

// Available timeframes
export const TIMEFRAMES = {
  '4h': '4h',
  '3h': '3h',
  '2h': '2h',
  '1d': '1d',
  '1w': '1w'
};

// CoinGecko API base URL
const COINGECKO_API_BASE = 'https://api.coingecko.com/api/v3';

/**
 * Fetch available crypto symbols from CoinGecko
 */
export const fetchCryptoSymbols = async (): Promise<CryptoSymbol[]> => {
  try {
    const response = await fetch(`${COINGECKO_API_BASE}/coins/list`);
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    const data = await response.json();
    return data.slice(0, 100); // Limit to first 100 for performance
  } catch (error) {
    console.error('Error fetching crypto symbols:', error);
    return [];
  }
};

/**
 * Fetch historical market data for a specific crypto
 * @param coinId - CoinGecko coin ID
 * @param days - Number of days of data to fetch
 * @param interval - Data interval (daily, hourly)
 */
export const fetchMarketChart = async (
  coinId: string,
  days: number = 30,
  interval: string = 'daily'
): Promise<CandleData[]> => {
  try {
    const response = await fetch(
      `${COINGECKO_API_BASE}/coins/${coinId}/market_chart?vs_currency=usd&days=${days}&interval=${interval}`
    );
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Transform the data into candle format
    // Note: CoinGecko doesn't provide OHLC directly in this endpoint
    // We're simulating candles from price points
    const candles: CandleData[] = [];
    
    if (data.prices && data.prices.length > 0) {
      for (let i = 0; i < data.prices.length; i++) {
        const timestamp = data.prices[i][0];
        const price = data.prices[i][1];
        const volume = data.total_volumes[i][1];
        
        // Create a simple candle where open/close/high/low are all the same price
        // In a real app, you'd use OHLC data or calculate from more granular data
        candles.push({
          time: new Date(timestamp).toISOString().split('T')[0],
          open: price,
          high: price * 1.005, // Simulate slight variation
          low: price * 0.995,  // Simulate slight variation
          close: price,
          volume: volume
        });
      }
    }
    
    return candles;
  } catch (error) {
    console.error('Error fetching market chart:', error);
    return [];
  }
};

/**
 * Fetch OHLC data for a specific crypto
 * @param coinId - CoinGecko coin ID
 * @param days - Number of days of data to fetch
 */
export const fetchOHLC = async (
  coinId: string,
  days: number = 30
): Promise<CandleData[]> => {
  try {
    const response = await fetch(
      `${COINGECKO_API_BASE}/coins/${coinId}/ohlc?vs_currency=usd&days=${days}`
    );
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Transform the data into our candle format
    return data.map((item: number[]) => ({
      time: new Date(item[0]).toISOString().split('T')[0],
      open: item[1],
      high: item[2],
      low: item[3],
      close: item[4]
    }));
  } catch (error) {
    console.error('Error fetching OHLC data:', error);
    return [];
  }
};

/**
 * Convert timeframe to days for API calls
 * @param timeframe - Timeframe string (e.g., '4h', '1d', '1w')
 */
export const timeframeToDays = (timeframe: string): number => {
  switch (timeframe) {
    case '4h':
      return 7; // 7 days of 4h candles
    case '3h':
      return 7; // 7 days of 3h candles
    case '2h':
      return 7; // 7 days of 2h candles
    case '1d':
      return 30; // 30 days of daily candles
    case '1w':
      return 90; // 90 days of weekly candles
    default:
      return 30;
  }
};

/**
 * Generate mock data when API limits are reached or for testing
 * @param days - Number of days to generate
 */
export const generateMockData = (days: number = 30): CandleData[] => {
  const data: CandleData[] = [];
  const basePrice = 50000;
  const now = new Date();
  
  for (let i = days; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(now.getDate() - i);
    
    const open = basePrice + Math.random() * 2000 - 1000;
    const close = open + Math.random() * 1000 - 500;
    const high = Math.max(open, close) + Math.random() * 500;
    const low = Math.min(open, close) - Math.random() * 500;
    const volume = Math.random() * 10000 + 5000;
    
    data.push({
      time: date.toISOString().split('T')[0],
      open,
      high,
      low,
      close,
      volume
    });
  }
  
  return data;
};
