!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)}(this,function(et,tt){"use strict";try{!(function(){function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var g,i,t,r,s,n=e(et);function o(t){const r=[];let s=0;for(let n=0;n<t.length;n++){let e=t.charCodeAt(n);e<128?r[s++]=e:(e<2048?r[s++]=e>>6|192:(55296==(64512&e)&&n+1<t.length&&56320==(64512&t.charCodeAt(n+1))?(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++n)),r[s++]=e>>18|240,r[s++]=e>>12&63|128):r[s++]=e>>12|224,r[s++]=e>>6&63|128),r[s++]=63&e|128)}return r}const a={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(r,e){if(!Array.isArray(r))throw Error("encodeByteArray takes an array as a parameter");this.init_();var s=e?this.byteToCharMapWebSafe_:this.byteToCharMap_;const n=[];for(let u=0;u<r.length;u+=3){var i=r[u],o=u+1<r.length,a=o?r[u+1]:0,h=u+2<r.length,l=h?r[u+2]:0;let e=(15&a)<<2|l>>6,t=63&l;h||(t=64,o||(e=64)),n.push(s[i>>2],s[(3&i)<<4|a>>4],s[e],s[t])}return n.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(o(e),t)},decodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?atob(e):function(e){const t=[];let r=0,s=0;for(;r<e.length;){var n,i,o=e[r++];o<128?t[s++]=String.fromCharCode(o):191<o&&o<224?(n=e[r++],t[s++]=String.fromCharCode((31&o)<<6|63&n)):239<o&&o<365?(i=((7&o)<<18|(63&e[r++])<<12|(63&e[r++])<<6|63&e[r++])-65536,t[s++]=String.fromCharCode(55296+(i>>10)),t[s++]=String.fromCharCode(56320+(1023&i))):(n=e[r++],i=e[r++],t[s++]=String.fromCharCode((15&o)<<12|(63&n)<<6|63&i))}return t.join("")}(this.decodeStringToByteArray(e,t))},decodeStringToByteArray(e,t){this.init_();var r=t?this.charToByteMapWebSafe_:this.charToByteMap_;const s=[];for(let h=0;h<e.length;){var n=r[e.charAt(h++)],i=h<e.length?r[e.charAt(h)]:0;++h;var o=h<e.length?r[e.charAt(h)]:64;++h;var a=h<e.length?r[e.charAt(h)]:64;if(++h,null==n||null==i||null==o||null==a)throw new l;s.push(n<<2|i>>4),64!==o&&(s.push(i<<4&240|o>>2),64!==a&&s.push(o<<6&192|a))}return s},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e,e>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class l extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}function h(e){return u(e).replace(/\./g,"")}const u=function(e){var t=o(e);return a.encodeByteArray(t,!0)};class c extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,c.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,d.prototype.create)}}class d{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var s,r=t[0]||{},n=`${this.service}/${e}`,i=this.errors[e],i=i?(s=r,i.replace(_,(e,t)=>{var r=s[t];return null!=r?String(r):`<${t}?>`})):"Error",i=`${this.serviceName}: ${i} (${n}).`;return new c(n,i,r)}}const _=/\{\$([^}]+)}/g;function p(e){return e&&e._delegate?e._delegate:e}class f{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}const m="firebasestorage.googleapis.com",b="storageBucket";class v extends c{constructor(e,t,r=0){super(T(e),`Firebase Storage: ${t} (${T(e)})`),this.status_=r,this.customData={serverResponse:null},this._baseMessage=this.message,Object.setPrototypeOf(this,v.prototype)}get status(){return this.status_}set status(e){this.status_=e}_codeEquals(e){return T(e)===this.code}get serverResponse(){return this.customData.serverResponse}set serverResponse(e){this.customData.serverResponse=e,this.customData.serverResponse?this.message=`${this._baseMessage}\n${this.customData.serverResponse}`:this.message=this._baseMessage}}function T(e){return"storage/"+e}function R(){return new v(g.UNKNOWN,"An unknown error occurred, please check the error payload for server response.")}function E(){return new v(g.RETRY_LIMIT_EXCEEDED,"Max retry time for operation exceeded, please try again.")}function w(){return new v(g.CANCELED,"User canceled the upload/download.")}function y(){return new v(g.CANNOT_SLICE_BLOB,"Cannot slice blob for upload. Please retry the upload.")}function k(e){return new v(g.INVALID_ARGUMENT,e)}function A(){return new v(g.APP_DELETED,"The Firebase app was deleted.")}function C(e){return new v(g.INVALID_ROOT_OPERATION,"The operation '"+e+"' cannot be performed on a root reference, create a non-root reference using child, such as .child('file.png').")}function S(e,t){return new v(g.INVALID_FORMAT,"String does not match format '"+e+"': "+t)}function O(e){throw new v(g.INTERNAL_ERROR,"Internal error: "+e)}(t=g=g||{}).UNKNOWN="unknown",t.OBJECT_NOT_FOUND="object-not-found",t.BUCKET_NOT_FOUND="bucket-not-found",t.PROJECT_NOT_FOUND="project-not-found",t.QUOTA_EXCEEDED="quota-exceeded",t.UNAUTHENTICATED="unauthenticated",t.UNAUTHORIZED="unauthorized",t.UNAUTHORIZED_APP="unauthorized-app",t.RETRY_LIMIT_EXCEEDED="retry-limit-exceeded",t.INVALID_CHECKSUM="invalid-checksum",t.CANCELED="canceled",t.INVALID_EVENT_NAME="invalid-event-name",t.INVALID_URL="invalid-url",t.INVALID_DEFAULT_BUCKET="invalid-default-bucket",t.NO_DEFAULT_BUCKET="no-default-bucket",t.CANNOT_SLICE_BLOB="cannot-slice-blob",t.SERVER_FILE_WRONG_SIZE="server-file-wrong-size",t.NO_DOWNLOAD_URL="no-download-url",t.INVALID_ARGUMENT="invalid-argument",t.INVALID_ARGUMENT_COUNT="invalid-argument-count",t.APP_DELETED="app-deleted",t.INVALID_ROOT_OPERATION="invalid-root-operation",t.INVALID_FORMAT="invalid-format",t.INTERNAL_ERROR="internal-error",t.UNSUPPORTED_ENVIRONMENT="unsupported-environment";class U{constructor(e,t){this.bucket=e,this.path_=t}get path(){return this.path_}get isRoot(){return 0===this.path.length}fullServerUrl(){const e=encodeURIComponent;return"/b/"+e(this.bucket)+"/o/"+e(this.path)}bucketOnlyServerUrl(){const e=encodeURIComponent;return"/b/"+e(this.bucket)+"/o"}static makeFromBucketSpec(t,e){let r;try{r=U.makeFromUrl(t,e)}catch(e){return new U(t,"")}if(""===r.path)return r;throw t=t,new v(g.INVALID_DEFAULT_BUCKET,"Invalid default bucket '"+t+"'.")}static makeFromUrl(e,t){let r=null;var s="([A-Za-z0-9.\\-_]+)";var n=new RegExp("^gs://"+s+"(/(.*))?$","i");function i(e){e.path_=decodeURIComponent(e.path)}var o=t.replace(/[.]/g,"\\."),a=new RegExp(`^https?://${o}/v[A-Za-z0-9_]+/b/${s}/o(/([^?#]*).*)?$`,"i"),o=t===m?"(?:storage.googleapis.com|storage.cloud.google.com)":t,h=[{regex:n,indices:{bucket:1,path:3},postModify:function(e){"/"===e.path.charAt(e.path.length-1)&&(e.path_=e.path_.slice(0,-1))}},{regex:a,indices:{bucket:1,path:3},postModify:i},{regex:new RegExp(`^https?://${o}/${s}/([^?#]*)`,"i"),indices:{bucket:1,path:2},postModify:i}];for(let c=0;c<h.length;c++){const d=h[c];var l=d.regex.exec(e);if(l){var u=l[d.indices.bucket];let e=l[d.indices.path];e=e||"",r=new U(u,e),d.postModify(r);break}}if(null==r)throw t=e,new v(g.INVALID_URL,"Invalid URL '"+t+"'.");return r}}class N{constructor(e){this.promise_=Promise.reject(e)}getPromise(){return this.promise_}cancel(e=0){}}function x(e){return"string"==typeof e||e instanceof String}function I(e){return P()&&e instanceof Blob}function P(){return"undefined"!=typeof Blob}function D(e,t,r,s){if(s<t)throw k(`Invalid value for '${e}'. Expected ${t} or greater.`);if(r<s)throw k(`Invalid value for '${e}'. Expected ${r} or less.`)}function L(e,t,r){let s=null==r?`https://${t}`:t;return`${r}://${s}/v0${e}`}function M(e){const t=encodeURIComponent;let r="?";for(const n in e){var s;e.hasOwnProperty(n)&&(s=t(n)+"="+t(e[n]),r=r+s+"&")}return r=r.slice(0,-1),r}function B(e,t){var r=500<=e&&e<600,s=-1!==[408,429].indexOf(e),n=-1!==t.indexOf(e);return r||s||n}(t=i=i||{})[t.NO_ERROR=0]="NO_ERROR",t[t.NETWORK_ERROR=1]="NETWORK_ERROR",t[t.ABORT=2]="ABORT";class F{constructor(e,t,r,s,n,i,o,a,h,l,u,c=!0){this.url_=e,this.method_=t,this.headers_=r,this.body_=s,this.successCodes_=n,this.additionalRetryCodes_=i,this.callback_=o,this.errorCallback_=a,this.timeout_=h,this.progressCallback_=l,this.connectionFactory_=u,this.retry=c,this.pendingConnection_=null,this.backoffId_=null,this.canceled_=!1,this.appDelete_=!1,this.promise_=new Promise((e,t)=>{this.resolve_=e,this.reject_=t,this.start_()})}start_(){var e=(r,e)=>{if(e)r(!1,new q(!1,null,!0));else{const s=this.connectionFactory_();this.pendingConnection_=s;const n=e=>{var t=e.loaded,r=e.lengthComputable?e.total:-1;null!==this.progressCallback_&&this.progressCallback_(t,r)};null!==this.progressCallback_&&s.addUploadProgressListener(n),s.send(this.url_,this.method_,this.body_,this.headers_).then(()=>{null!==this.progressCallback_&&s.removeUploadProgressListener(n),this.pendingConnection_=null;var e=s.getErrorCode()===i.NO_ERROR,t=s.getStatus();!e||B(t,this.additionalRetryCodes_)&&this.retry?(e=s.getErrorCode()===i.ABORT,r(!1,new q(!1,null,e))):(t=-1!==this.successCodes_.indexOf(t),r(!0,new q(t,s)))})}},t=(e,t)=>{const r=this.resolve_,s=this.reject_,n=t.connection;if(t.wasSuccessCode)try{var i=this.callback_(n,n.getResponse());void 0!==i?r(i):r()}catch(e){s(e)}else if(null!==n){const a=R();a.serverResponse=n.getErrorText(),this.errorCallback_?s(this.errorCallback_(n,a)):s(a)}else{var o;t.canceled?(o=(this.appDelete_?A:w)(),s(o)):(o=E(),s(o))}};this.canceled_?t(0,new q(!1,null,!0)):this.backoffId_=function(t,r,e){let s=1,n=null,i=null,o=!1,a=0;function h(){return 2===a}let l=!1;function u(...e){l||(l=!0,r.apply(null,e))}function c(e){n=setTimeout(()=>{n=null,t(_,h())},e)}function d(){i&&clearTimeout(i)}function _(t,...r){if(l)d();else{if(t)return d(),void u.call(null,t,...r);if(h()||o)return d(),void u.call(null,t,...r);s<64&&(s*=2);let e;e=1===a?(a=2,0):1e3*(s+Math.random()),c(e)}}let p=!1;function f(e){p||(p=!0,d(),l||(null!==n?(e||(a=2),clearTimeout(n),c(0)):e||(a=1)))}return c(0),i=setTimeout(()=>{f(o=!0)},e),f}(e,t,this.timeout_)}getPromise(){return this.promise_}cancel(e){this.canceled_=!0,this.appDelete_=e||!1,null!==this.backoffId_&&(0,this.backoffId_)(!1),null!==this.pendingConnection_&&this.pendingConnection_.abort()}}class q{constructor(e,t,r){this.wasSuccessCode=e,this.connection=t,this.canceled=!!r}}function V(...t){const r="undefined"!=typeof BlobBuilder?BlobBuilder:"undefined"!=typeof WebKitBlobBuilder?WebKitBlobBuilder:void 0;if(void 0!==r){const s=new r;for(let e=0;e<t.length;e++)s.append(t[e]);return s.getBlob()}if(P())return new Blob(t);throw new v(g.UNSUPPORTED_ENVIRONMENT,"This browser doesn't seem to support creating Blobs")}function H(e){if("undefined"==typeof atob)throw new v(g.UNSUPPORTED_ENVIRONMENT,"base-64 is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.");return atob(e)}const j={RAW:"raw",BASE64:"base64",BASE64URL:"base64url",DATA_URL:"data_url"};class W{constructor(e,t){this.data=e,this.contentType=t||null}}function z(e,t){switch(e){case j.RAW:return new W($(t));case j.BASE64:case j.BASE64URL:return new W(G(e,t));case j.DATA_URL:return new W((r=t,(s=new X(r)).base64?G(j.BASE64,s.rest):function(e){let t;try{t=decodeURIComponent(e)}catch(e){throw S(j.DATA_URL,"Malformed data URL.")}return $(t)}(s.rest)),(r=t,new X(r).contentType))}var r,s;throw R()}function $(t){const r=[];for(let i=0;i<t.length;i++){let e=t.charCodeAt(i);var s,n;e<=127?r.push(e):e<=2047?r.push(192|e>>6,128|63&e):55296==(64512&e)?i<t.length-1&&56320==(64512&t.charCodeAt(i+1))?(s=e,n=t.charCodeAt(++i),e=65536|(1023&s)<<10|1023&n,r.push(240|e>>18,128|e>>12&63,128|e>>6&63,128|63&e)):r.push(239,191,189):56320==(64512&e)?r.push(239,191,189):r.push(224|e>>12,128|e>>6&63,128|63&e)}return new Uint8Array(r)}function G(t,e){switch(t){case j.BASE64:var r=-1!==e.indexOf("-"),s=-1!==e.indexOf("_");if(r||s)throw S(t,"Invalid character '"+(r?"-":"_")+"' found: is it base64url encoded?");break;case j.BASE64URL:s=-1!==e.indexOf("+"),r=-1!==e.indexOf("/");if(s||r)throw S(t,"Invalid character '"+(s?"+":"/")+"' found: is it base64 encoded?");e=e.replace(/-/g,"+").replace(/_/g,"/")}let n;try{n=H(e)}catch(e){if(e.message.includes("polyfill"))throw e;throw S(t,"Invalid character found")}const i=new Uint8Array(n.length);for(let o=0;o<n.length;o++)i[o]=n.charCodeAt(o);return i}class X{constructor(e){this.base64=!1,this.contentType=null;var t,r,s=e.match(/^data:([^,]+)?,/);if(null===s)throw S(j.DATA_URL,"Must be formatted 'data:[<mediatype>][;base64],<data>");const n=s[1]||null;null!=n&&(this.base64=(t=n,r=";base64",t.length>=r.length&&t.substring(t.length-r.length)===r),this.contentType=this.base64?n.substring(0,n.length-";base64".length):n),this.rest=e.substring(e.indexOf(",")+1)}}class K{constructor(e,t){let r=0,s="";I(e)?(this.data_=e,r=e.size,s=e.type):e instanceof ArrayBuffer?(t?this.data_=new Uint8Array(e):(this.data_=new Uint8Array(e.byteLength),this.data_.set(new Uint8Array(e))),r=this.data_.length):e instanceof Uint8Array&&(t?this.data_=e:(this.data_=new Uint8Array(e.length),this.data_.set(e)),r=e.length),this.size_=r,this.type_=s}size(){return this.size_}type(){return this.type_}slice(e,t){if(I(this.data_)){var r=this.data_,r=(n=e,i=t,(s=r).webkitSlice?s.webkitSlice(n,i):s.mozSlice?s.mozSlice(n,i):s.slice?s.slice(n,i):null);return null===r?null:new K(r)}var s,n,i,r=new Uint8Array(this.data_.buffer,e,t-e);return new K(r,!0)}static getBlob(...e){if(P()){var t=e.map(e=>e instanceof K?e.data_:e);return new K(V.apply(null,t))}{const s=e.map(e=>x(e)?z(j.RAW,e).data:e.data_);let t=0;s.forEach(e=>{t+=e.byteLength});const n=new Uint8Array(t);let r=0;return s.forEach(e=>{for(let t=0;t<e.length;t++)n[r++]=e[t]}),new K(n,!0)}}uploadData(){return this.data_}}function Z(e){let t;try{t=JSON.parse(e)}catch(e){return null}return"object"!=typeof(e=t)||Array.isArray(e)?null:t}function J(e){var t=e.lastIndexOf("/",e.length-2);return-1===t?e:e.slice(t+1)}function Y(e,t){return t}class Q{constructor(e,t,r,s){this.server=e,this.local=t||e,this.writable=!!r,this.xform=s||Y}}let ee=null;function te(){if(ee)return ee;const e=[];e.push(new Q("bucket")),e.push(new Q("generation")),e.push(new Q("metageneration")),e.push(new Q("name","fullPath",!0));const t=new Q("name");t.xform=function(e,t){return!x(t=t)||t.length<2?t:J(t)},e.push(t);const r=new Q("size");return r.xform=function(e,t){return void 0!==t?Number(t):t},e.push(r),e.push(new Q("timeCreated")),e.push(new Q("updated")),e.push(new Q("md5Hash",null,!0)),e.push(new Q("cacheControl",null,!0)),e.push(new Q("contentDisposition",null,!0)),e.push(new Q("contentEncoding",null,!0)),e.push(new Q("contentLanguage",null,!0)),e.push(new Q("contentType",null,!0)),e.push(new Q("metadata","customMetadata",!0)),ee=e,ee}function re(r,s){Object.defineProperty(r,"ref",{get:function(){var e=r.bucket,t=r.fullPath,t=new U(e,t);return s._makeStorageReference(t)}})}function se(e,t,r){var s=Z(t);return null===s?null:function(e,t,r){const s={type:"file"};var n=r.length;for(let i=0;i<n;i++){const o=r[i];s[o.local]=o.xform(s,t[o.server])}return re(s,e),s}(e,s,r)}function ne(e,t){const r={};var s=t.length;for(let i=0;i<s;i++){var n=t[i];n.writable&&(r[n.server]=e[n.local])}return JSON.stringify(r)}const ie="prefixes";function oe(e,t,r){var s=Z(r);return null===s?null:function(e,t,r){const s={prefixes:[],items:[],nextPageToken:r.nextPageToken};if(r[ie])for(const o of r[ie]){var n=o.replace(/\/$/,""),n=e._makeStorageReference(new U(t,n));s.prefixes.push(n)}if(r.items)for(const a of r.items){var i=e._makeStorageReference(new U(t,a.name));s.items.push(i)}return s}(e,t,s)}class ae{constructor(e,t,r,s){this.url=e,this.method=t,this.handler=r,this.timeout=s,this.urlParams={},this.headers={},this.body=null,this.errorHandler=null,this.progressCallback=null,this.successCodes=[200],this.additionalRetryCodes=[]}}function he(e){if(!e)throw R()}function le(s,n){return function(e,t){var r=se(s,t,n);return he(null!==r),r}}function ue(s,n){return function(e,t){var r=oe(s,n,t);return he(null!==r),r}}function ce(s,n){return function(e,t){var r=se(s,t,n);return he(null!==r),function(s,e,n,i){var t=Z(e);if(null===t)return null;if(!x(t.downloadTokens))return null;const r=t.downloadTokens;if(0===r.length)return null;const o=encodeURIComponent,a=r.split(",");return a.map(e=>{var t=s.bucket,r=s.fullPath;return L("/b/"+o(t)+"/o/"+o(r),n,i)+M({alt:"media",token:e})})[0]}(r,t,s.host,s._protocol)}}function de(n){return function(e,t){let r;var s;return r=401===e.getStatus()?e.getErrorText().includes("Firebase App Check token is invalid")?new v(g.UNAUTHORIZED_APP,"This app does not have permission to access Firebase Storage on this project."):new v(g.UNAUTHENTICATED,"User is not authenticated, please authenticate using Firebase Authentication and try again."):402===e.getStatus()?(s=n.bucket,new v(g.QUOTA_EXCEEDED,"Quota for bucket '"+s+"' exceeded, please view quota on https://firebase.google.com/pricing/.")):403===e.getStatus()?(s=n.path,new v(g.UNAUTHORIZED,"User does not have permission to access '"+s+"'.")):t,r.status=e.getStatus(),r.serverResponse=t.serverResponse,r}}function _e(s){const n=de(s);return function(e,t){let r=n(e,t);return 404===e.getStatus()&&(r=(e=s.path,new v(g.OBJECT_NOT_FOUND,"Object '"+e+"' does not exist."))),r.serverResponse=t.serverResponse,r}}function pe(e,t,r){var s=L(t.fullServerUrl(),e.host,e._protocol),n=e.maxOperationRetryTime;const i=new ae(s,"GET",le(e,r),n);return i.errorHandler=_e(t),i}function fe(e,t,r){const s=Object.assign({},r);return s.fullPath=e.path,s.size=t.size(),s.contentType||(s.contentType=(e=t,(t=null)&&t.contentType||e&&e.type()||"application/octet-stream")),s}function ge(e,t,r,s,n){var i=t.bucketOnlyServerUrl();const o={"X-Goog-Upload-Protocol":"multipart"};var a=function(){let e="";for(let t=0;t<2;t++)e+=Math.random().toString().slice(2);return e}();o["Content-Type"]="multipart/related; boundary="+a;var h=fe(t,s,n),l="--"+a+"\r\nContent-Type: application/json; charset=utf-8\r\n\r\n"+ne(h,r)+"\r\n--"+a+"\r\nContent-Type: "+h.contentType+"\r\n\r\n",a="\r\n--"+a+"--";const u=K.getBlob(l,s,a);if(null===u)throw y();a={name:h.fullPath},h=L(i,e.host,e._protocol),i=e.maxUploadRetryTime;const c=new ae(h,"POST",le(e,r),i);return c.urlParams=a,c.headers=o,c.body=u.uploadData(),c.errorHandler=de(t),c}class me{constructor(e,t,r,s){this.current=e,this.total=t,this.finalized=!!r,this.metadata=s||null}}function be(e,t){let r=null;try{r=e.getResponseHeader("X-Goog-Upload-Status")}catch(e){he(!1)}const s=t||["active"];return he(!!r&&-1!==s.indexOf(r)),r}function ve(e,t,r,s,n){var i=t.bucketOnlyServerUrl(),o=fe(t,s,n),a={name:o.fullPath},h=L(i,e.host,e._protocol),l={"X-Goog-Upload-Protocol":"resumable","X-Goog-Upload-Command":"start","X-Goog-Upload-Header-Content-Length":`${s.size()}`,"X-Goog-Upload-Header-Content-Type":o.contentType,"Content-Type":"application/json; charset=utf-8"},i=ne(o,r),o=e.maxUploadRetryTime;const u=new ae(h,"POST",function(e){be(e);let t;try{t=e.getResponseHeader("X-Goog-Upload-URL")}catch(e){he(!1)}return he(x(t)),t},o);return u.urlParams=a,u.headers=l,u.body=i,u.errorHandler=de(t),u}function Te(e,t,r,n){var s=e.maxUploadRetryTime;const i=new ae(r,"POST",function(e){var t=be(e,["active","final"]);let r=null;try{r=e.getResponseHeader("X-Goog-Upload-Size-Received")}catch(e){he(!1)}r||he(!1);var s=Number(r);return he(!isNaN(s)),new me(s,n.size(),"final"===t)},s);return i.headers={"X-Goog-Upload-Command":"query"},i.errorHandler=de(t),i}function Re(e,o,t,a,r,h,s,n){const l=new me(0,0);if(s?(l.current=s.current,l.total=s.total):(l.current=0,l.total=a.size()),a.size()!==l.total)throw new v(g.SERVER_FILE_WRONG_SIZE,"Server recorded incorrect upload file size, please retry the upload.");var i=l.total-l.current;let u=i;0<r&&(u=Math.min(u,r));var c=l.current,d=c+u;let _="";_=0===u?"finalize":i===u?"upload, finalize":"upload";i={"X-Goog-Upload-Command":_,"X-Goog-Upload-Offset":`${l.current}`};const p=a.slice(c,d);if(null===p)throw y();d=o.maxUploadRetryTime;const f=new ae(t,"POST",function(e,t){var r=be(e,["active","final"]),s=l.current+u,n=a.size();let i;return i="final"===r?le(o,h)(e,t):null,new me(s,n,"final"===r,i)},d);return f.headers=i,f.body=p.uploadData(),f.progressCallback=n||null,f.errorHandler=de(e),f}const Ee={STATE_CHANGED:"state_changed"},we={RUNNING:"running",PAUSED:"paused",SUCCESS:"success",CANCELED:"canceled",ERROR:"error"};function ye(e){switch(e){case"running":case"pausing":case"canceling":return we.RUNNING;case"paused":return we.PAUSED;case"success":return we.SUCCESS;case"canceled":return we.CANCELED;default:return we.ERROR}}class ke{constructor(e,t,r){var s;"function"==typeof e||null!=t||null!=r?(this.next=e,this.error=null!=t?t:void 0,this.complete=null!=r?r:void 0):(this.next=(s=e).next,this.error=s.error,this.complete=s.complete)}}function Ae(t){return(...e)=>{Promise.resolve().then(()=>t(...e))}}class Ce extends class{constructor(){this.sent_=!1,this.xhr_=new XMLHttpRequest,this.initXhr(),this.errorCode_=i.NO_ERROR,this.sendPromise_=new Promise(e=>{this.xhr_.addEventListener("abort",()=>{this.errorCode_=i.ABORT,e()}),this.xhr_.addEventListener("error",()=>{this.errorCode_=i.NETWORK_ERROR,e()}),this.xhr_.addEventListener("load",()=>{e()})})}send(e,t,r,s){if(this.sent_)throw O("cannot .send() more than once");if(this.sent_=!0,this.xhr_.open(t,e,!0),void 0!==s)for(const n in s)s.hasOwnProperty(n)&&this.xhr_.setRequestHeader(n,s[n].toString());return void 0!==r?this.xhr_.send(r):this.xhr_.send(),this.sendPromise_}getErrorCode(){if(!this.sent_)throw O("cannot .getErrorCode() before sending");return this.errorCode_}getStatus(){if(!this.sent_)throw O("cannot .getStatus() before sending");try{return this.xhr_.status}catch(e){return-1}}getResponse(){if(!this.sent_)throw O("cannot .getResponse() before sending");return this.xhr_.response}getErrorText(){if(!this.sent_)throw O("cannot .getErrorText() before sending");return this.xhr_.statusText}abort(){this.xhr_.abort()}getResponseHeader(e){return this.xhr_.getResponseHeader(e)}addUploadProgressListener(e){null!=this.xhr_.upload&&this.xhr_.upload.addEventListener("progress",e)}removeUploadProgressListener(e){null!=this.xhr_.upload&&this.xhr_.upload.removeEventListener("progress",e)}}{initXhr(){this.xhr_.responseType="text"}}function Se(){return new Ce}class Oe{constructor(e,t,r=null){this._transferred=0,this._needToFetchStatus=!1,this._needToFetchMetadata=!1,this._observers=[],this._error=void 0,this._uploadUrl=void 0,this._request=void 0,this._chunkMultiplier=1,this._resolve=void 0,this._reject=void 0,this._ref=e,this._blob=t,this._metadata=r,this._mappings=te(),this._resumable=this._shouldDoResumable(this._blob),this._state="running",this._errorHandler=e=>{if(this._request=void 0,this._chunkMultiplier=1,e._codeEquals(g.CANCELED))this._needToFetchStatus=!0,this.completeTransitions_();else{var t=this.isExponentialBackoffExpired();if(B(e.status,[])){if(!t)return this.sleepTime=Math.max(2*this.sleepTime,1e3),this._needToFetchStatus=!0,void this.completeTransitions_();e=E()}this._error=e,this._transition("error")}},this._metadataErrorHandler=e=>{this._request=void 0,e._codeEquals(g.CANCELED)?this.completeTransitions_():(this._error=e,this._transition("error"))},this.sleepTime=0,this.maxSleepTime=this._ref.storage.maxUploadRetryTime,this._promise=new Promise((e,t)=>{this._resolve=e,this._reject=t,this._start()}),this._promise.then(null,()=>{})}isExponentialBackoffExpired(){return this.sleepTime>this.maxSleepTime}_makeProgressCallback(){const t=this._transferred;return e=>this._updateProgress(t+e)}_shouldDoResumable(e){return 262144<e.size()}_start(){"running"===this._state&&void 0===this._request&&(this._resumable?void 0===this._uploadUrl?this._createResumable():this._needToFetchStatus?this._fetchStatus():this._needToFetchMetadata?this._fetchMetadata():this.pendingTimeout=setTimeout(()=>{this.pendingTimeout=void 0,this._continueUpload()},this.sleepTime):this._oneShotUpload())}_resolveToken(r){Promise.all([this._ref.storage._getAuthToken(),this._ref.storage._getAppCheckToken()]).then(([e,t])=>{switch(this._state){case"running":r(e,t);break;case"canceling":this._transition("canceled");break;case"pausing":this._transition("paused")}})}_createResumable(){this._resolveToken((e,t)=>{var r=ve(this._ref.storage,this._ref._location,this._mappings,this._blob,this._metadata);const s=this._ref.storage._makeRequest(r,Se,e,t);this._request=s,s.getPromise().then(e=>{this._request=void 0,this._uploadUrl=e,this._needToFetchStatus=!1,this.completeTransitions_()},this._errorHandler)})}_fetchStatus(){const n=this._uploadUrl;this._resolveToken((e,t)=>{var r=Te(this._ref.storage,this._ref._location,n,this._blob);const s=this._ref.storage._makeRequest(r,Se,e,t);this._request=s,s.getPromise().then(e=>{this._request=void 0,this._updateProgress(e.current),this._needToFetchStatus=!1,e.finalized&&(this._needToFetchMetadata=!0),this.completeTransitions_()},this._errorHandler)})}_continueUpload(){const n=262144*this._chunkMultiplier,i=new me(this._transferred,this._blob.size()),o=this._uploadUrl;this._resolveToken((e,t)=>{let r;try{r=Re(this._ref._location,this._ref.storage,o,this._blob,n,this._mappings,i,this._makeProgressCallback())}catch(e){return this._error=e,void this._transition("error")}const s=this._ref.storage._makeRequest(r,Se,e,t,!1);this._request=s,s.getPromise().then(e=>{this._increaseMultiplier(),this._request=void 0,this._updateProgress(e.current),e.finalized?(this._metadata=e.metadata,this._transition("success")):this.completeTransitions_()},this._errorHandler)})}_increaseMultiplier(){2*(262144*this._chunkMultiplier)<33554432&&(this._chunkMultiplier*=2)}_fetchMetadata(){this._resolveToken((e,t)=>{var r=pe(this._ref.storage,this._ref._location,this._mappings);const s=this._ref.storage._makeRequest(r,Se,e,t);this._request=s,s.getPromise().then(e=>{this._request=void 0,this._metadata=e,this._transition("success")},this._metadataErrorHandler)})}_oneShotUpload(){this._resolveToken((e,t)=>{var r=ge(this._ref.storage,this._ref._location,this._mappings,this._blob,this._metadata);const s=this._ref.storage._makeRequest(r,Se,e,t);this._request=s,s.getPromise().then(e=>{this._request=void 0,this._metadata=e,this._updateProgress(this._blob.size()),this._transition("success")},this._errorHandler)})}_updateProgress(e){var t=this._transferred;this._transferred=e,this._transferred!==t&&this._notifyObservers()}_transition(e){if(this._state!==e)switch(e){case"canceling":case"pausing":this._state=e,void 0!==this._request?this._request.cancel():this.pendingTimeout&&(clearTimeout(this.pendingTimeout),this.pendingTimeout=void 0,this.completeTransitions_());break;case"running":var t="paused"===this._state;this._state=e,t&&(this._notifyObservers(),this._start());break;case"paused":this._state=e,this._notifyObservers();break;case"canceled":this._error=w(),this._state=e,this._notifyObservers();break;case"error":case"success":this._state=e,this._notifyObservers()}}completeTransitions_(){switch(this._state){case"pausing":this._transition("paused");break;case"canceling":this._transition("canceled");break;case"running":this._start()}}get snapshot(){var e=ye(this._state);return{bytesTransferred:this._transferred,totalBytes:this._blob.size(),state:e,metadata:this._metadata,task:this,ref:this._ref}}on(e,t,r,s){const n=new ke(t||void 0,r||void 0,s||void 0);return this._addObserver(n),()=>{this._removeObserver(n)}}then(e,t){return this._promise.then(e,t)}catch(e){return this.then(null,e)}_addObserver(e){this._observers.push(e),this._notifyObserver(e)}_removeObserver(e){var t=this._observers.indexOf(e);-1!==t&&this._observers.splice(t,1)}_notifyObservers(){this._finishPromise();const e=this._observers.slice();e.forEach(e=>{this._notifyObserver(e)})}_finishPromise(){if(void 0!==this._resolve){let e=!0;switch(ye(this._state)){case we.SUCCESS:Ae(this._resolve.bind(null,this.snapshot))();break;case we.CANCELED:case we.ERROR:const t=this._reject;Ae(t.bind(null,this._error))();break;default:e=!1}e&&(this._resolve=void 0,this._reject=void 0)}}_notifyObserver(e){switch(ye(this._state)){case we.RUNNING:case we.PAUSED:e.next&&Ae(e.next.bind(e,this.snapshot))();break;case we.SUCCESS:e.complete&&Ae(e.complete.bind(e))();break;case we.CANCELED:case we.ERROR:default:e.error&&Ae(e.error.bind(e,this._error))()}}resume(){var e="paused"===this._state||"pausing"===this._state;return e&&this._transition("running"),e}pause(){var e="running"===this._state;return e&&this._transition("pausing"),e}cancel(){var e="running"===this._state||"pausing"===this._state;return e&&this._transition("canceling"),e}}class Ue{constructor(e,t){this._service=e,t instanceof U?this._location=t:this._location=U.makeFromUrl(t,e.host)}toString(){return"gs://"+this._location.bucket+"/"+this._location.path}_newRef(e,t){return new Ue(e,t)}get root(){var e=new U(this._location.bucket,"");return this._newRef(this._service,e)}get bucket(){return this._location.bucket}get fullPath(){return this._location.path}get name(){return J(this._location.path)}get storage(){return this._service}get parent(){var e=function(e){if(0===e.length)return null;var t=e.lastIndexOf("/");return-1===t?"":e.slice(0,t)}(this._location.path);if(null===e)return null;e=new U(this._location.bucket,e);return new Ue(this._service,e)}_throwIfRoot(e){if(""===this._location.path)throw C(e)}}function Ne(e){const t={prefixes:[],items:[]};return async function e(t,r,s){const n={pageToken:s};const i=await xe(t,n);r.prefixes.push(...i.prefixes);r.items.push(...i.items);null!=i.nextPageToken&&await e(t,r,i.nextPageToken)}(e,t).then(()=>t)}function xe(e,t){null!=t&&"number"==typeof t.maxResults&&D("options.maxResults",1,1e3,t.maxResults);var r=t||{},r=function(e,t,r,s,n){const i={};t.isRoot?i.prefix="":i.prefix=t.path+"/",r&&0<r.length&&(i.delimiter=r),s&&(i.pageToken=s),n&&(i.maxResults=n);var o=L(t.bucketOnlyServerUrl(),e.host,e._protocol),a=e.maxOperationRetryTime;const h=new ae(o,"GET",ue(e,t.bucket),a);return h.urlParams=i,h.errorHandler=de(t),h}(e.storage,e._location,"/",r.pageToken,r.maxResults);return e.storage.makeRequestWithTokens(r,Se)}function Ie(e,t){e._throwIfRoot("updateMetadata");var r=function(e,t,r,s){var n=L(t.fullServerUrl(),e.host,e._protocol),i=ne(r,s),o=e.maxOperationRetryTime;const a=new ae(n,"PATCH",le(e,s),o);return a.headers={"Content-Type":"application/json; charset=utf-8"},a.body=i,a.errorHandler=_e(t),a}(e.storage,e._location,t,te());return e.storage.makeRequestWithTokens(r,Se)}function Pe(e){e._throwIfRoot("getDownloadURL");var t=function(e,t,r){var s=L(t.fullServerUrl(),e.host,e._protocol),n=e.maxOperationRetryTime;const i=new ae(s,"GET",ce(e,r),n);return i.errorHandler=_e(t),i}(e.storage,e._location,te());return e.storage.makeRequestWithTokens(t,Se).then(e=>{if(null===e)throw new v(g.NO_DOWNLOAD_URL,"The given file does not have any download URLs.");return e})}function De(e){e._throwIfRoot("deleteObject");var t=function(e,t){var r=L(t.fullServerUrl(),e.host,e._protocol),s=e.maxOperationRetryTime;const n=new ae(r,"DELETE",function(e,t){},s);return n.successCodes=[200,204],n.errorHandler=_e(t),n}(e.storage,e._location);return e.storage.makeRequestWithTokens(t,Se)}function Le(e,t){var r,s=(r=e._location.path,s=t.split("/").filter(e=>0<e.length).join("/"),0===r.length?s:r+"/"+s),s=new U(e._location.bucket,s);return new Ue(e.storage,s)}function Me(e,t){if(e instanceof Ve){var r=e;if(null==r._bucket)throw new v(g.NO_DEFAULT_BUCKET,"No default bucket found. Did you set the '"+b+"' property when initializing the app?");r=new Ue(r,r._bucket);return null!=t?Me(r,t):r}return void 0!==t?Le(e,t):e}function Be(e,t){if(t&&/^[A-Za-z]+:\/\//.test(t)){if(e instanceof Ve)return r=e,s=t,new Ue(r,s);throw k("To use ref(service, url), the first argument must be a Storage instance.")}return Me(e,t);var r,s}function Fe(e,t){var r=null==t?void 0:t[b];return null==r?null:U.makeFromBucketSpec(r,e)}function qe(e,t,r,s={}){e.host=`${t}:${r}`,e._protocol="http";var n=s["mockUserToken"];n&&(e._overrideAuthToken="string"==typeof n?n:function(e,t){if(e.uid)throw new Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');var r=t||"demo-project",s=e.iat||0,n=e.sub||e.user_id;if(!n)throw new Error("mockUserToken must contain 'sub' or 'user_id' field!");return n=Object.assign({iss:`https://securetoken.google.com/${r}`,aud:r,iat:s,exp:s+3600,auth_time:s,sub:n,user_id:n,firebase:{sign_in_provider:"custom",identities:{}}},e),[h(JSON.stringify({alg:"none",type:"JWT"})),h(JSON.stringify(n)),""].join(".")}(n,e.app.options.projectId))}class Ve{constructor(e,t,r,s,n){this.app=e,this._authProvider=t,this._appCheckProvider=r,this._url=s,this._firebaseVersion=n,this._bucket=null,this._host=m,this._protocol="https",this._appId=null,this._deleted=!1,this._maxOperationRetryTime=12e4,this._maxUploadRetryTime=6e5,this._requests=new Set,this._bucket=null!=s?U.makeFromBucketSpec(s,this._host):Fe(this._host,this.app.options)}get host(){return this._host}set host(e){this._host=e,null!=this._url?this._bucket=U.makeFromBucketSpec(this._url,e):this._bucket=Fe(e,this.app.options)}get maxUploadRetryTime(){return this._maxUploadRetryTime}set maxUploadRetryTime(e){D("time",0,Number.POSITIVE_INFINITY,e),this._maxUploadRetryTime=e}get maxOperationRetryTime(){return this._maxOperationRetryTime}set maxOperationRetryTime(e){D("time",0,Number.POSITIVE_INFINITY,e),this._maxOperationRetryTime=e}async _getAuthToken(){if(this._overrideAuthToken)return this._overrideAuthToken;const e=this._authProvider.getImmediate({optional:!0});if(e){var t=await e.getToken();if(null!==t)return t.accessToken}return null}async _getAppCheckToken(){const e=this._appCheckProvider.getImmediate({optional:!0});return e?(await e.getToken()).token:null}_delete(){return this._deleted||(this._deleted=!0,this._requests.forEach(e=>e.cancel()),this._requests.clear()),Promise.resolve()}_makeStorageReference(e){return new Ue(this,e)}_makeRequest(e,t,r,s,n=!0){if(this._deleted)return new N(A());{const u=([i,o,a,e,r,s,t=!0]=[e,this._appId,r,s,t,this._firebaseVersion,n],h=M(i.urlParams),l=i.url+h,h=Object.assign({},i.headers),n=h,(o=o)&&(n["X-Firebase-GMPID"]=o),o=h,null!==(a=a)&&0<a.length&&(o.Authorization="Firebase "+a),s=s,h["X-Firebase-Storage-Version"]="webjs/"+(null!=s?s:"AppManager"),s=h,null!==(e=e)&&(s["X-Firebase-AppCheck"]=e),new F(l,i.method,h,i.body,i.successCodes,i.additionalRetryCodes,i.handler,i.errorHandler,i.timeout,i.progressCallback,r,t));return this._requests.add(u),u.getPromise().then(()=>this._requests.delete(u),()=>this._requests.delete(u)),u}var i,o,a,h,l}async makeRequestWithTokens(e,t){var[r,s]=await Promise.all([this._getAuthToken(),this._getAppCheckToken()]);return this._makeRequest(e,t,r,s).getPromise()}}const He="@firebase/storage";function je(e,t,r){return e=p(e),t=t,r=r,(e=e)._throwIfRoot("uploadBytesResumable"),new Oe(e,new K(t),r)}function We(e){return function(e){e._throwIfRoot("getMetadata");var t=pe(e.storage,e._location,te());return e.storage.makeRequestWithTokens(t,Se)}(e=p(e))}function ze(e,t){return Be(e=p(e),t)}function $e(e,{instanceIdentifier:t}){var r=e.getProvider("app").getImmediate(),s=e.getProvider("auth-internal"),n=e.getProvider("app-check-internal");return new Ve(r,s,n,t,tt.SDK_VERSION)}tt._registerComponent(new f("storage",$e,"PUBLIC").setMultipleInstances(!0)),tt.registerVersion(He,"0.13.2",""),tt.registerVersion(He,"0.13.2","esm2017");class Ge{constructor(e,t,r){this._delegate=e,this.task=t,this.ref=r}get bytesTransferred(){return this._delegate.bytesTransferred}get metadata(){return this._delegate.metadata}get state(){return this._delegate.state}get totalBytes(){return this._delegate.totalBytes}}class Xe{constructor(e,t){this._delegate=e,this._ref=t,this.cancel=this._delegate.cancel.bind(this._delegate),this.catch=this._delegate.catch.bind(this._delegate),this.pause=this._delegate.pause.bind(this._delegate),this.resume=this._delegate.resume.bind(this._delegate)}get snapshot(){return new Ge(this._delegate.snapshot,this,this._ref)}then(t,e){return this._delegate.then(e=>{if(t)return t(new Ge(e,this,this._ref))},e)}on(e,t,r,s){let n=void 0;return t&&(n="function"==typeof t?e=>t(new Ge(e,this,this._ref)):{next:t.next?e=>t.next(new Ge(e,this,this._ref)):void 0,complete:t.complete||void 0,error:t.error||void 0}),this._delegate.on(e,n,r||void 0,s||void 0)}}class Ke{constructor(e,t){this._delegate=e,this._service=t}get prefixes(){return this._delegate.prefixes.map(e=>new Ze(e,this._service))}get items(){return this._delegate.items.map(e=>new Ze(e,this._service))}get nextPageToken(){return this._delegate.nextPageToken||null}}class Ze{constructor(e,t){this._delegate=e,this.storage=t}get name(){return this._delegate.name}get bucket(){return this._delegate.bucket}get fullPath(){return this._delegate.fullPath}toString(){return this._delegate.toString()}child(e){var t=Le(this._delegate,e);return new Ze(t,this.storage)}get root(){return new Ze(this._delegate.root,this.storage)}get parent(){var e=this._delegate.parent;return null==e?null:new Ze(e,this.storage)}put(e,t){return this._throwIfRoot("put"),new Xe(je(this._delegate,e,t),this)}putString(e,t=j.RAW,r){this._throwIfRoot("putString");var s=z(t,e);const n=Object.assign({},r);return null==n.contentType&&null!=s.contentType&&(n.contentType=s.contentType),new Xe(new Oe(this._delegate,new K(s.data,!0),n),this)}listAll(){return Ne(p(this._delegate)).then(e=>new Ke(e,this.storage))}list(e){return t=this._delegate,e=e||void 0,xe(t=p(t),e).then(e=>new Ke(e,this.storage));var t}getMetadata(){return We(this._delegate)}updateMetadata(e){return Ie(p(this._delegate),e)}getDownloadURL(){return Pe(p(this._delegate))}delete(){return this._throwIfRoot("delete"),De(p(this._delegate))}_throwIfRoot(e){if(""===this._delegate._location.path)throw C(e)}}class Je{constructor(e,t){this.app=e,this._delegate=t}get maxOperationRetryTime(){return this._delegate.maxOperationRetryTime}get maxUploadRetryTime(){return this._delegate.maxUploadRetryTime}ref(e){if(Ye(e))throw k("ref() expected a child path but got a URL, use refFromURL instead.");return new Ze(ze(this._delegate,e),this)}refFromURL(e){if(!Ye(e))throw k("refFromURL() expected a full URL but got a child path, use ref() instead.");try{U.makeFromUrl(e,this._delegate.host)}catch(e){throw k("refFromUrl() expected a valid full URL but got an invalid one.")}return new Ze(ze(this._delegate,e),this)}setMaxUploadRetryTime(e){this._delegate.maxUploadRetryTime=e}setMaxOperationRetryTime(e){this._delegate.maxOperationRetryTime=e}useEmulator(e,t,r={}){var s;[s,e,t,r={}]=[this._delegate,e,t,r],qe(s,e,t,r)}}function Ye(e){return/^[A-Za-z]+:\/\//.test(e)}function Qe(e,{instanceIdentifier:t}){var r=e.getProvider("app-compat").getImmediate(),s=e.getProvider("storage").getImmediate({identifier:t});return new Je(r,s)}r=n.default,s={TaskState:we,TaskEvent:Ee,StringFormat:j,Storage:Je,Reference:Ze},r.INTERNAL.registerComponent(new f("storage-compat",Qe,"PUBLIC").setServiceProps(s).setMultipleInstances(!0)),r.registerVersion("@firebase/storage-compat","0.3.12")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-storage-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-storage-compat.js.map
