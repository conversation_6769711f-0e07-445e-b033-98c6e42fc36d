import React, { useState } from 'react';
import { useAuth } from '../services/auth';

interface AuthFormProps {
  onSuccess: () => void;
}

const AuthForm: React.FC<AuthFormProps> = ({ onSuccess }) => {
  const [isLogin, setIsLogin] = useState<boolean>(true);
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const { login, signup, error, loading } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isLogin) {
      await login(email, password);
    } else {
      await signup(email, password);
    }
    
    if (!error) {
      onSuccess();
    }
  };

  return (
    <div className="auth-form card">
      <div className="card-header">
        <h3 className="text-center">
          {isLogin ? 'تسجيل الدخول / Login' : 'إنشاء حساب / Sign Up'}
        </h3>
      </div>
      <div className="card-body">
        {error && (
          <div className="alert alert-danger" role="alert">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit}>
          <div className="mb-3">
            <label htmlFor="email" className="form-label">
              البريد الإلكتروني / Email
            </label>
            <input
              type="email"
              className="form-control"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          
          <div className="mb-3">
            <label htmlFor="password" className="form-label">
              كلمة المرور / Password
            </label>
            <input
              type="password"
              className="form-control"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          
          <div className="d-grid gap-2">
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
              ) : isLogin ? (
                'تسجيل الدخول / Login'
              ) : (
                'إنشاء حساب / Sign Up'
              )}
            </button>
          </div>
        </form>
      </div>
      <div className="card-footer text-center">
        <button
          className="btn btn-link"
          onClick={() => setIsLogin(!isLogin)}
        >
          {isLogin ? (
            'ليس لديك حساب؟ إنشاء حساب جديد / Create new account'
          ) : (
            'لديك حساب بالفعل؟ تسجيل الدخول / Login'
          )}
        </button>
      </div>
    </div>
  );
};

export default AuthForm;
