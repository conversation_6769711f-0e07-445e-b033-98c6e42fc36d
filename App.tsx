import React, { useState, useEffect } from 'react';
import CandlestickChart from './components/CandlestickChart';
import TechnicalIndicators from './components/TechnicalIndicators';
import SignalsList from './components/SignalsList';
import LanguageSelector from './components/LanguageSelector';
import AuthForm from './components/AuthForm';
import UserSettingsForm from './components/UserSettingsForm';
import NotificationsComponent from './components/NotificationsComponent';
import { fetchOHLC, generateMockData, timeframeToDays, CandleData } from './services/api';
import { 
  calculateRSI, 
  calculateMACD, 
  calculateOBV, 
  calculateMovingAverages,
  detectRSIDivergence,
  detectMACrossover,
  Signal
} from './services/technicalAnalysis';
import { AuthProvider, useAuth } from './services/auth';
import { translate, getLanguageDirection } from './services/i18n';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';

// المكون الرئيسي للتطبيق
const AppContent: React.FC = () => {
  const { currentUser, userSettings, logout } = useAuth();
  
  // حالة اللغة الحالية
  const [language, setLanguage] = useState<string>(userSettings.preferredLanguage);
  
  // حالة الإطار الزمني الحالي
  const [timeframe, setTimeframe] = useState<string>(userSettings.preferredTimeframe);
  
  // حالة المؤشرات الفنية النشطة
  const [indicators, setIndicators] = useState(userSettings.activeIndicators);
  
  // حالة العملة المختارة
  const [selectedCoin, setSelectedCoin] = useState<string>('bitcoin');
  
  // بيانات الشموع اليابانية
  const [candleData, setCandleData] = useState<CandleData[]>([]);
  
  // إشارات البيع والشراء
  const [signals, setSignals] = useState<Signal[]>([]);
  
  // حالة التحميل
  const [loading, setLoading] = useState<boolean>(false);
  
  // حالة الخطأ
  const [error, setError] = useState<string | null>(null);
  
  // حالة عرض نموذج الإعدادات
  const [showSettings, setShowSettings] = useState<boolean>(false);

  // تحديث الإعدادات عند تغيير المستخدم
  useEffect(() => {
    if (currentUser) {
      setLanguage(userSettings.preferredLanguage);
      setTimeframe(userSettings.preferredTimeframe);
      setIndicators(userSettings.activeIndicators);
    }
  }, [currentUser, userSettings]);

  // تغيير اتجاه الصفحة بناءً على اللغة
  useEffect(() => {
    document.documentElement.setAttribute('dir', getLanguageDirection(language));
    document.documentElement.setAttribute('lang', language);
  }, [language]);

  // تحميل البيانات عند بدء التطبيق أو تغيير العملة أو الإطار الزمني
  useEffect(() => {
    loadMarketData();
  }, [selectedCoin, timeframe]);
  
  // تحليل البيانات وإنشاء الإشارات عند تغيير البيانات أو المؤشرات النشطة
  useEffect(() => {
    if (candleData.length > 0) {
      analyzeData();
    }
  }, [candleData, indicators]);

  // تحميل بيانات السوق
  const loadMarketData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const days = timeframeToDays(timeframe);
      let data = await fetchOHLC(selectedCoin, days);
      
      // إذا فشل جلب البيانات، استخدم بيانات تجريبية
      if (data.length === 0) {
        data = generateMockData(days);
      }
      
      setCandleData(data);
    } catch (err) {
      console.error('Error loading market data:', err);
      setError(translate('loadingError', language));
      
      // استخدم بيانات تجريبية في حالة الخطأ
      const days = timeframeToDays(timeframe);
      setCandleData(generateMockData(days));
    } finally {
      setLoading(false);
    }
  };

  // تحليل البيانات وإنشاء الإشارات
  const analyzeData = () => {
    const allSignals: Signal[] = [];
    
    // حساب المؤشرات الفنية
    const rsiData = calculateRSI(candleData);
    const macdData = calculateMACD(candleData);
    const obvData = calculateOBV(candleData);
    const maData = calculateMovingAverages(candleData);
    
    // اكتشاف الإشارات بناءً على المؤشرات النشطة
    if (indicators.rsiDivergence) {
      const rsiSignals = detectRSIDivergence(candleData, rsiData);
      allSignals.push(...rsiSignals);
    }
    
    if (indicators.movingAverageCrossover) {
      const maSignals = detectMACrossover(maData, candleData);
      allSignals.push(...maSignals);
    }
    
    // ترتيب الإشارات حسب التاريخ (الأحدث أولاً)
    allSignals.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    setSignals(allSignals);
  };

  // تغيير الإطار الزمني
  const handleTimeframeChange = (newTimeframe: string) => {
    setTimeframe(newTimeframe);
  };

  // تبديل حالة المؤشرات الفنية
  const handleToggleIndicator = (indicator: string) => {
    setIndicators(prev => ({
      ...prev,
      [indicator]: !prev[indicator as keyof typeof prev]
    }));
  };

  // تغيير اللغة
  const handleLanguageChange = (newLanguage: string) => {
    setLanguage(newLanguage);
  };

  // إذا لم يكن هناك مستخدم مسجل، عرض نموذج المصادقة
  if (!currentUser) {
    return (
      <div className="container mt-5">
        <div className="row justify-content-center">
          <div className="col-md-6">
            <AuthForm onSuccess={() => {}} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="App">
      <header className="bg-dark text-white p-3 mb-4">
        <div className="container">
          <div className="d-flex justify-content-between align-items-center">
            <h1>
              {translate('appTitle', language)}
            </h1>
            <div className="d-flex">
              <button 
                className="btn btn-outline-light me-2" 
                onClick={() => setShowSettings(true)}
              >
                {translate('settings', language)}
              </button>
              <button 
                className="btn btn-outline-light" 
                onClick={logout}
              >
                {translate('logout', language)}
              </button>
            </div>
          </div>
        </div>
        <LanguageSelector currentLanguage={language} onLanguageChange={handleLanguageChange} />
      </header>

      {showSettings && (
        <div className="modal-backdrop show"></div>
      )}
      
      {showSettings && (
        <div className="modal show d-block" tabIndex={-1}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <UserSettingsForm onClose={() => setShowSettings(false)} />
            </div>
          </div>
        </div>
      )}

      <div className="container">
        {loading && (
          <div className="alert alert-info text-center">
            {translate('loading', language)}
          </div>
        )}
        
        {error && (
          <div className="alert alert-danger text-center">
            {error}
          </div>
        )}
        
        <NotificationsComponent signals={signals} language={language} />
        
        <div className="row">
          <div className="col-md-9">
            <div className="card mb-4">
              <div className="card-body">
                <CandlestickChart 
                  data={candleData} 
                  timeframe={timeframe} 
                  onTimeframeChange={handleTimeframeChange} 
                />
              </div>
            </div>
          </div>
          
          <div className="col-md-3">
            <TechnicalIndicators 
              indicators={indicators} 
              onToggleIndicator={handleToggleIndicator} 
            />
            
            <SignalsList signals={signals} />
          </div>
        </div>
      </div>
      
      <footer className="bg-light p-3 mt-4">
        <div className="container text-center">
          <p>
            {translate('copyright', language)}
          </p>
        </div>
      </footer>
    </div>
  );
};

// المكون الرئيسي مع مزود المصادقة
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
