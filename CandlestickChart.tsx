import React, { useEffect, useRef } from 'react';
import { createChart, IChartApi, ISeriesApi, CandlestickData } from 'lightweight-charts';

interface CandlestickChartProps {
  data: CandlestickData[];
  timeframe: string;
  onTimeframeChange: (timeframe: string) => void;
}

const CandlestickChart: React.FC<CandlestickChartProps> = ({ data, timeframe, onTimeframeChange }) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const seriesRef = useRef<ISeriesApi<"Candlestick"> | null>(null);

  // الأطر الزمنية المتاحة
  const timeframes = ['4h', '3h', '2h', '1d', '1w'];

  useEffect(() => {
    if (chartContainerRef.current) {
      // إنشاء الرسم البياني
      const chart = createChart(chartContainerRef.current, {
        width: chartContainerRef.current.clientWidth,
        height: 500,
        layout: {
          background: { color: '#ffffff' },
          textColor: '#333',
        },
        grid: {
          vertLines: { color: '#f0f0f0' },
          horzLines: { color: '#f0f0f0' },
        },
        timeScale: {
          borderColor: '#d1d1d1',
        },
      });

      // إضافة سلسلة الشموع اليابانية
      const candlestickSeries = chart.addCandlestickSeries({
        upColor: '#26a69a',
        downColor: '#ef5350',
        borderVisible: false,
        wickUpColor: '#26a69a',
        wickDownColor: '#ef5350',
      });

      // تعيين البيانات
      candlestickSeries.setData(data);

      // تخزين المراجع للاستخدام لاحقاً
      chartRef.current = chart;
      seriesRef.current = candlestickSeries;

      // تعديل حجم الرسم البياني عند تغيير حجم النافذة
      const handleResize = () => {
        if (chartRef.current && chartContainerRef.current) {
          chartRef.current.applyOptions({
            width: chartContainerRef.current.clientWidth,
          });
        }
      };

      window.addEventListener('resize', handleResize);

      // تنظيف عند إزالة المكون
      return () => {
        window.removeEventListener('resize', handleResize);
        if (chartRef.current) {
          chartRef.current.remove();
        }
      };
    }
  }, []);

  // تحديث البيانات عند تغيير الإطار الزمني
  useEffect(() => {
    if (seriesRef.current) {
      seriesRef.current.setData(data);
    }
  }, [data]);

  return (
    <div className="chart-component">
      <div className="timeframe-selector d-flex justify-content-center mb-3">
        {timeframes.map((tf) => (
          <button
            key={tf}
            className={`btn ${timeframe === tf ? 'btn-primary' : 'btn-outline-secondary'} mx-1`}
            onClick={() => onTimeframeChange(tf)}
          >
            {tf}
          </button>
        ))}
      </div>
      <div ref={chartContainerRef} className="chart-container" />
    </div>
  );
};

export default CandlestickChart;
