import React, { useState, useEffect } from 'react';
import { initializeNotifications, sendSignalNotification, Notification } from '../services/notifications';
import { useAuth } from '../services/auth';
import { Signal } from '../services/technicalAnalysis';

interface NotificationsComponentProps {
  signals: Signal[];
  language: string;
}

const NotificationsComponent: React.FC<NotificationsComponentProps> = ({ signals, language }) => {
  const { currentUser, userSettings } = useAuth();
  const [notificationsEnabled, setNotificationsEnabled] = useState<boolean>(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [showNotifications, setShowNotifications] = useState<boolean>(false);
  const [initializingNotifications, setInitializingNotifications] = useState<boolean>(false);

  // تهيئة الإشعارات عند تحميل المكون
  useEffect(() => {
    if (currentUser && userSettings.notificationsEnabled) {
      setupNotifications();
    }
  }, [currentUser, userSettings.notificationsEnabled]);

  // إرسال إشعارات للإشارات الجديدة
  useEffect(() => {
    if (notificationsEnabled && signals.length > 0 && userSettings.notificationsEnabled) {
      // إرسال إشعار للإشارة الأحدث فقط
      const latestSignal = signals[0];
      sendSignalNotification(latestSignal, language);
    }
  }, [signals, notificationsEnabled, language]);

  // تهيئة نظام الإشعارات
  const setupNotifications = async () => {
    setInitializingNotifications(true);
    try {
      const enabled = await initializeNotifications();
      setNotificationsEnabled(enabled);
    } catch (error) {
      console.error('فشل في تهيئة الإشعارات:', error);
    } finally {
      setInitializingNotifications(false);
    }
  };

  // طلب إذن الإشعارات
  const requestNotificationPermission = async () => {
    setInitializingNotifications(true);
    try {
      const result = await Notification.requestPermission();
      if (result === 'granted') {
        await setupNotifications();
      }
    } catch (error) {
      console.error('فشل في طلب إذن الإشعارات:', error);
    } finally {
      setInitializingNotifications(false);
    }
  };

  return (
    <div className="notifications-component">
      {!notificationsEnabled && !initializingNotifications && (
        <div className="alert alert-info">
          <div className="d-flex justify-content-between align-items-center">
            <span>
              {language === 'ar' 
                ? 'الإشعارات غير مفعلة. فعّل الإشعارات لتلقي تنبيهات عند ظهور إشارات جديدة.' 
                : 'Notifications are disabled. Enable notifications to receive alerts for new signals.'}
            </span>
            <button 
              className="btn btn-sm btn-primary" 
              onClick={requestNotificationPermission}
            >
              {language === 'ar' ? 'تفعيل الإشعارات' : 'Enable Notifications'}
            </button>
          </div>
        </div>
      )}

      {initializingNotifications && (
        <div className="alert alert-warning">
          {language === 'ar' ? 'جاري تهيئة الإشعارات...' : 'Initializing notifications...'}
        </div>
      )}

      {notificationsEnabled && (
        <div className="alert alert-success">
          {language === 'ar' 
            ? 'الإشعارات مفعلة. ستتلقى تنبيهات عند ظهور إشارات جديدة.' 
            : 'Notifications are enabled. You will receive alerts for new signals.'}
        </div>
      )}
    </div>
  );
};

export default NotificationsComponent;
